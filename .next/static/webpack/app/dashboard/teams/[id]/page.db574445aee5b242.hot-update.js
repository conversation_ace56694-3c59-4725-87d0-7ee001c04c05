"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/api/teams.ts":
/*!******************************!*\
  !*** ./src/lib/api/teams.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   teamsApi: function() { return /* binding */ teamsApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\n// Helper function to get auth headers\nconst getAuthHeaders = ()=>{\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    if (true) {\n        try {\n            const authStorage = localStorage.getItem(\"auth-storage\");\n            if (authStorage) {\n                var _parsed_state;\n                const parsed = JSON.parse(authStorage);\n                const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                if (token) {\n                    headers.Authorization = \"Bearer \".concat(token);\n                    return headers;\n                }\n            }\n        } catch (error) {\n            console.warn(\"Failed to parse auth storage:\", error);\n        }\n        // Fallback to direct localStorage access\n        const fallbackToken = localStorage.getItem(\"accessToken\");\n        if (fallbackToken) {\n            headers.Authorization = \"Bearer \".concat(fallbackToken);\n            return headers;\n        }\n    }\n    return headers;\n};\nconst teamsApi = {\n    // Use Next.js API proxy (similar to leagues)\n    getTeams: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await fetch(\"/api/teams?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch teams\");\n        }\n        return await response.json();\n    },\n    // Use Next.js API proxy for team details\n    getTeamById: async (externalId)=>{\n        const response = await fetch(\"/api/teams/\".concat(externalId), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch team details\");\n        }\n        const data = await response.json();\n        return data.data; // Extract team data from API response\n    },\n    // Requires authentication\n    getTeamStatistics: async (league, season, team)=>{\n        const params = new URLSearchParams({\n            league: league.toString(),\n            season: season.toString(),\n            team: team.toString()\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/teams/statistics?\".concat(params.toString()));\n        return response;\n    },\n    // Helper methods for common operations\n    getTeamsByLeague: async (league, season)=>{\n        const filters = {\n            league\n        };\n        if (season) filters.season = season;\n        return teamsApi.getTeams(filters);\n    },\n    getTeamsByCountry: async (country)=>{\n        return teamsApi.getTeams({\n            country\n        });\n    },\n    searchTeams: async function(query) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        // Note: This would need to be implemented on the backend\n        // For now, we'll get all teams and filter client-side (not ideal for production)\n        const teams = await teamsApi.getTeams(filters);\n        const filteredTeams = teams.data.filter((team)=>{\n            var _team_code;\n            return team.name.toLowerCase().includes(query.toLowerCase()) || ((_team_code = team.code) === null || _team_code === void 0 ? void 0 : _team_code.toLowerCase().includes(query.toLowerCase()));\n        });\n        return {\n            data: filteredTeams,\n            meta: {\n                ...teams.meta,\n                totalItems: filteredTeams.length,\n                totalPages: Math.ceil(filteredTeams.length / (filters.limit || 10))\n            }\n        };\n    },\n    // Get team leagues and seasons participation\n    getTeamLeaguesSeasons: async function(externalId) {\n        let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"by-league\";\n        const response = await fetch(\"/api/teams/\".concat(externalId, \"/leagues-seasons?format=\").concat(format), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch team leagues and seasons\");\n        }\n        const data = await response.json();\n        return data.data; // Extract team leagues-seasons data from API response\n    },\n    // Sync team data from external source (requires authentication)\n    syncTeam: async (externalId)=>{\n        const response = await fetch(\"/api/teams/\".concat(externalId, \"?newdb=true\"), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to sync team data\");\n        }\n        const data = await response.json();\n        return data.data; // Extract team data from API response\n    },\n    // Delete team (requires admin access)\n    deleteTeam: async (externalId)=>{\n        await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/football/teams/\".concat(externalId));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/teams.ts\n"));

/***/ })

});
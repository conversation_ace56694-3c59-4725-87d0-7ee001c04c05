"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/teams/[id]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/teams/[id]/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TeamDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useTeams */ \"(app-pages-browser)/./src/lib/hooks/useTeams.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _lib_utils_image__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/image */ \"(app-pages-browser)/./src/lib/utils/image.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TeamDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { isEditor, isAdmin } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions)();\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQueryClient)();\n    const teamId = parseInt(params.id);\n    // Fetch team details\n    const { team, isLoading, error, refetch } = (0,_lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__.useTeam)(teamId);\n    // Debug logging\n    console.log(\"\\uD83D\\uDD0D Team Details Debug:\", {\n        teamId,\n        team,\n        isLoading,\n        error: error instanceof Error ? error.message : error,\n        hasTeam: !!team\n    });\n    // Force refetch on mount if no data\n    React.useEffect(()=>{\n        if (!isLoading && !team && !error) {\n            console.log(\"\\uD83D\\uDD04 Force refetch team data\");\n            refetch();\n        }\n    }, [\n        teamId,\n        isLoading,\n        team,\n        error,\n        refetch\n    ]);\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.deleteTeam(teamId),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"Team deleted successfully\");\n            setDeleteModalOpen(false);\n            router.push(\"/dashboard/teams\");\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_12__.toast.error(error.message || \"Failed to delete team\");\n            setDeleteModalOpen(false);\n        }\n    });\n    // Handlers\n    const handleEdit = ()=>{\n        router.push(\"/dashboard/teams/\".concat(teamId, \"/edit\"));\n    };\n    const handleDelete = ()=>{\n        setDeleteModalOpen(true);\n    };\n    const handleViewStatistics = ()=>{\n        router.push(\"/dashboard/teams/\".concat(teamId, \"/statistics\"));\n    };\n    const confirmDelete = ()=>{\n        deleteMutation.mutate();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-8 w-48\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 25\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                className: \"h-96\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n            lineNumber: 98,\n            columnNumber: 19\n        }, this);\n    }\n    if (error || !team) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 37\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 31\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 25\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"flex items-center justify-center h-96\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"Team not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"The team you're looking for doesn't exist or you don't have permission to view it.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 43\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 37\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 31\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n            lineNumber: 119,\n            columnNumber: 19\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.back(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 37\n                                    }, this),\n                                    \"Back\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) || \"\",\n                                        alt: team.name,\n                                        className: \"w-12 h-12 object-contain rounded-full\",\n                                        onError: (e)=>{\n                                            const target = e.target;\n                                            target.style.display = \"none\";\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 43\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: team.name\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 text-gray-600\",\n                                                children: [\n                                                    team.country ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildCountryFlagUrl)(team.country) || \"\",\n                                                                alt: \"\".concat(team.country, \" flag\"),\n                                                                className: \"w-4 h-3 object-cover\",\n                                                                onError: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.display = \"none\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: team.country\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 italic\",\n                                                        children: \"Country not available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 55\n                                                    }, this),\n                                                    team.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-mono text-sm\",\n                                                                children: team.code\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    team.national && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs\",\n                                                                children: \"National Team\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 43\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 31\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleViewStatistics,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 37\n                                    }, this),\n                                    \"Statistics\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 31\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleEdit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 37\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleDelete,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                lineNumber: 151,\n                columnNumber: 19\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 49\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Team Information\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 49\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 43\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Team Name\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium mt-1\",\n                                                            children: team.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Team Code\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.code ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium font-mono mt-1\",\n                                                            children: team.code\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Country\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.country ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildCountryFlagUrl)(team.country) || \"\",\n                                                                    alt: \"\".concat(team.country, \" flag\"),\n                                                                    className: \"w-6 h-4 object-cover\",\n                                                                    onError: (e)=>{\n                                                                        const target = e.target;\n                                                                        target.style.display = \"none\";\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-medium\",\n                                                                    children: team.country\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Founded\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.founded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium mt-1\",\n                                                            children: team.founded\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Team Type\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                            children: team.national ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"default\",\n                                                                className: \"bg-blue-100 text-blue-800\",\n                                                                children: \"National Team\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 67\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: \"Club Team\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 67\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Home Venue\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.venue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium mt-1\",\n                                                            children: team.venue\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 43\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-500 mb-4 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        \"System Information\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"External ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        team.externalId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"Internal ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        team.id\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.season && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"Season\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: team.season\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 61\n                                                        }, this),\n                                                        team.leagueId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"League ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        team.leagueId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 43\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 31\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 49\n                                                }, this),\n                                                \"Team Logo\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 43\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) || \"\",\n                                                    alt: team.name,\n                                                    className: \"w-32 h-32 object-contain\",\n                                                    onError: (e)=>{\n                                                        const target = e.target;\n                                                        target.src = \"\";\n                                                        target.style.display = \"none\";\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 55\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-32 h-32 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-16 h-16 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 55\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 43\n                                            }, this),\n                                            team.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 break-all\",\n                                                    children: team.logo\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 55\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 49\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 49\n                                                }, this),\n                                                \"Quick Info\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 43\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Country\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: team.country ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildCountryFlagUrl)(team.country) || \"\",\n                                                                    alt: \"\".concat(team.country, \" flag\"),\n                                                                    className: \"w-4 h-3 object-cover\",\n                                                                    onError: (e)=>{\n                                                                        const target = e.target;\n                                                                        target.style.display = \"none\";\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: team.country\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-400 italic\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Founded\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    team.founded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"font-mono\",\n                                                        children: team.founded\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 55\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-400 italic\",\n                                                        children: \"Not available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Code\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    team.code ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"font-mono\",\n                                                        children: team.code\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 55\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-400 italic\",\n                                                        children: \"Not available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Type\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    team.national ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        className: \"bg-blue-100 text-blue-800\",\n                                                        children: \"National\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 55\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: \"Club\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Team ID\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium font-mono\",\n                                                        children: [\n                                                            \"#\",\n                                                            team.externalId\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 43\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 31\n                            }, this),\n                            (team.venue || team.season || team.leagueId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 55\n                                                }, this),\n                                                \"Additional Info\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            team.venue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            \"Venue\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: team.venue\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 55\n                                            }, this),\n                                            team.season && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            \"Season\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"font-mono\",\n                                                        children: team.season\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 55\n                                            }, this),\n                                            team.leagueId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            \"League ID\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium font-mono\",\n                                                        children: [\n                                                            \"#\",\n                                                            team.leagueId\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 55\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 43\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                lineNumber: 253,\n                columnNumber: 19\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>setDeleteModalOpen(false),\n                title: \"Delete Team\",\n                description: \"Are you sure you want to delete this team? This action cannot be undone.\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-600 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"This will permanently delete the team:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 43\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: team.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 49\n                                                }, this),\n                                                \" \",\n                                                team.country ? \"(\".concat(team.country, \")\") : \"(Country not available)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 43\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteModalOpen(false),\n                                    disabled: deleteMutation.isLoading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDelete,\n                                    disabled: deleteMutation.isLoading,\n                                    children: deleteMutation.isLoading ? \"Deleting...\" : \"Delete Team\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                lineNumber: 567,\n                columnNumber: 19\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n        lineNumber: 149,\n        columnNumber: 13\n    }, this);\n}\n_s(TeamDetailPage, \"XbzTRipxG32HRlwiB8JjPMqruqs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQueryClient,\n        _lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__.useTeam,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = TeamDetailPage;\nvar _c;\n$RefreshReg$(_c, \"TeamDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/teams/[id]/page.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/useTeams.ts":
/*!***********************************!*\
  !*** ./src/lib/hooks/useTeams.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSearchTeams: function() { return /* binding */ useSearchTeams; },\n/* harmony export */   useTeam: function() { return /* binding */ useTeam; },\n/* harmony export */   useTeamLeaguesSeasons: function() { return /* binding */ useTeamLeaguesSeasons; },\n/* harmony export */   useTeamMutations: function() { return /* binding */ useTeamMutations; },\n/* harmony export */   useTeamStatistics: function() { return /* binding */ useTeamStatistics; },\n/* harmony export */   useTeams: function() { return /* binding */ useTeams; },\n/* harmony export */   useTeamsByCountry: function() { return /* binding */ useTeamsByCountry; },\n/* harmony export */   useTeamsByLeague: function() { return /* binding */ useTeamsByLeague; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n\n\nconst useTeams = function() {\n    let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _teamsQuery_data, _teamsQuery_data1;\n    const teamsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            filters\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeams(filters),\n        staleTime: 10 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsQuery_data = teamsQuery.data) === null || _teamsQuery_data === void 0 ? void 0 : _teamsQuery_data.data) || [],\n        teamsMeta: (_teamsQuery_data1 = teamsQuery.data) === null || _teamsQuery_data1 === void 0 ? void 0 : _teamsQuery_data1.meta,\n        isLoading: teamsQuery.isLoading,\n        error: teamsQuery.error,\n        refetch: teamsQuery.refetch\n    };\n};\nconst useTeam = (externalId)=>{\n    const teamQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"team\",\n            \"detail\",\n            externalId\n        ],\n        queryFn: async ()=>{\n            console.log(\"\\uD83D\\uDD04 useTeam - Fetching team with ID:\", externalId);\n            try {\n                const result = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamById(externalId);\n                console.log(\"✅ useTeam - Success:\", result);\n                return result;\n            } catch (error) {\n                console.error(\"❌ useTeam - Error:\", error);\n                throw error;\n            }\n        },\n        enabled: !!externalId && externalId > 0,\n        staleTime: 5 * 60 * 1000,\n        retry: (failureCount, error)=>{\n            console.log(\"\\uD83D\\uDD04 useTeam - Retry attempt:\", failureCount, error);\n            return failureCount < 3;\n        },\n        retryDelay: 1000\n    });\n    console.log(\"\\uD83D\\uDD0D useTeam - Query state:\", {\n        externalId,\n        data: teamQuery.data,\n        isLoading: teamQuery.isLoading,\n        error: teamQuery.error,\n        status: teamQuery.status,\n        fetchStatus: teamQuery.fetchStatus\n    });\n    return {\n        team: teamQuery.data,\n        isLoading: teamQuery.isLoading,\n        error: teamQuery.error,\n        refetch: teamQuery.refetch\n    };\n};\nconst useTeamsByLeague = (league, season)=>{\n    var _teamsByLeagueQuery_data, _teamsByLeagueQuery_data1;\n    const teamsByLeagueQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"by-league\",\n            league,\n            season\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamsByLeague(league, season),\n        enabled: !!league,\n        staleTime: 15 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsByLeagueQuery_data = teamsByLeagueQuery.data) === null || _teamsByLeagueQuery_data === void 0 ? void 0 : _teamsByLeagueQuery_data.data) || [],\n        teamsMeta: (_teamsByLeagueQuery_data1 = teamsByLeagueQuery.data) === null || _teamsByLeagueQuery_data1 === void 0 ? void 0 : _teamsByLeagueQuery_data1.meta,\n        isLoading: teamsByLeagueQuery.isLoading,\n        error: teamsByLeagueQuery.error,\n        refetch: teamsByLeagueQuery.refetch\n    };\n};\nconst useTeamsByCountry = (country)=>{\n    var _teamsByCountryQuery_data, _teamsByCountryQuery_data1;\n    const teamsByCountryQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"by-country\",\n            country\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamsByCountry(country),\n        enabled: !!country,\n        staleTime: 15 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsByCountryQuery_data = teamsByCountryQuery.data) === null || _teamsByCountryQuery_data === void 0 ? void 0 : _teamsByCountryQuery_data.data) || [],\n        teamsMeta: (_teamsByCountryQuery_data1 = teamsByCountryQuery.data) === null || _teamsByCountryQuery_data1 === void 0 ? void 0 : _teamsByCountryQuery_data1.meta,\n        isLoading: teamsByCountryQuery.isLoading,\n        error: teamsByCountryQuery.error,\n        refetch: teamsByCountryQuery.refetch\n    };\n};\nconst useTeamMutations = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const deleteTeam = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (externalId)=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.deleteTeam(externalId),\n        onSuccess: ()=>{\n            // Invalidate and refetch teams data\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n        }\n    });\n    const syncTeam = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (externalId)=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.syncTeam(externalId),\n        onSuccess: (data, externalId)=>{\n            // Invalidate and refetch team data\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"team\",\n                    \"detail\",\n                    externalId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n            // Update the cache with new data\n            queryClient.setQueryData([\n                \"team\",\n                \"detail\",\n                externalId\n            ], data);\n        }\n    });\n    return {\n        deleteTeam: deleteTeam.mutate,\n        isDeleteLoading: deleteTeam.isLoading,\n        deleteError: deleteTeam.error,\n        syncTeam: syncTeam.mutate,\n        isSyncLoading: syncTeam.isLoading,\n        syncError: syncTeam.error\n    };\n};\nconst useTeamLeaguesSeasons = function(externalId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"by-league\";\n    const leaguesSeasonsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"team\",\n            \"leagues-seasons\",\n            externalId,\n            format\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamLeaguesSeasons(externalId, format),\n        enabled: !!externalId && externalId > 0,\n        staleTime: 10 * 60 * 1000\n    });\n    return {\n        leaguesSeasons: leaguesSeasonsQuery.data,\n        isLoading: leaguesSeasonsQuery.isLoading,\n        error: leaguesSeasonsQuery.error,\n        refetch: leaguesSeasonsQuery.refetch\n    };\n};\nconst useTeamStatistics = (teamId, leagueId, season)=>{\n    const statisticsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"team\",\n            \"statistics\",\n            teamId,\n            leagueId,\n            season\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamStatistics(teamId, leagueId, season),\n        enabled: !!teamId && teamId > 0 && !!leagueId && leagueId > 0 && !!season && season > 0,\n        staleTime: 15 * 60 * 1000,\n        retry: 2\n    });\n    return {\n        statistics: statisticsQuery.data,\n        isLoading: statisticsQuery.isLoading,\n        error: statisticsQuery.error,\n        refetch: statisticsQuery.refetch\n    };\n};\nconst useSearchTeams = function(query) {\n    let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _searchQuery_data, _searchQuery_data1;\n    const searchQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"search\",\n            query,\n            filters\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.searchTeams(query, filters),\n        enabled: !!query && query.length >= 2,\n        staleTime: 2 * 60 * 1000\n    });\n    return {\n        searchResults: ((_searchQuery_data = searchQuery.data) === null || _searchQuery_data === void 0 ? void 0 : _searchQuery_data.data) || [],\n        searchMeta: (_searchQuery_data1 = searchQuery.data) === null || _searchQuery_data1 === void 0 ? void 0 : _searchQuery_data1.meta,\n        isSearchLoading: searchQuery.isLoading,\n        searchError: searchQuery.error,\n        refetchSearch: searchQuery.refetch\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/useTeams.ts\n"));

/***/ })

});
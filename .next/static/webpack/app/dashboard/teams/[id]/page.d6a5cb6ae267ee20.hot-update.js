"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/api/teams.ts":
/*!******************************!*\
  !*** ./src/lib/api/teams.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   teamsApi: function() { return /* binding */ teamsApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\n// Helper function to get auth headers\nconst getAuthHeaders = ()=>{\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    if (true) {\n        try {\n            const authStorage = localStorage.getItem(\"auth-storage\");\n            if (authStorage) {\n                var _parsed_state;\n                const parsed = JSON.parse(authStorage);\n                const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                if (token) {\n                    headers.Authorization = \"Bearer \".concat(token);\n                    return headers;\n                }\n            }\n        } catch (error) {\n            console.warn(\"Failed to parse auth storage:\", error);\n        }\n        // Fallback to direct localStorage access\n        const fallbackToken = localStorage.getItem(\"accessToken\");\n        if (fallbackToken) {\n            headers.Authorization = \"Bearer \".concat(fallbackToken);\n            return headers;\n        }\n    }\n    return headers;\n};\nconst teamsApi = {\n    // Use Next.js API proxy (similar to leagues)\n    getTeams: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        // Get token from auth store for authorization\n        const getAuthHeaders = ()=>{\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            if (true) {\n                try {\n                    const authStorage = localStorage.getItem(\"auth-storage\");\n                    if (authStorage) {\n                        var _parsed_state;\n                        const parsed = JSON.parse(authStorage);\n                        const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                        if (token) {\n                            headers.Authorization = \"Bearer \".concat(token);\n                            return headers;\n                        }\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse auth storage:\", error);\n                }\n                // Fallback to direct localStorage access\n                const fallbackToken = localStorage.getItem(\"accessToken\");\n                if (fallbackToken) {\n                    headers.Authorization = \"Bearer \".concat(fallbackToken);\n                    return headers;\n                }\n            }\n            return headers;\n        };\n        const response = await fetch(\"/api/teams?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch teams\");\n        }\n        return await response.json();\n    },\n    // Use Next.js API proxy for team details\n    getTeamById: async (externalId)=>{\n        const response = await fetch(\"/api/teams/\".concat(externalId), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch team details\");\n        }\n        const data = await response.json();\n        return data.data; // Extract team data from API response\n    },\n    // Requires authentication\n    getTeamStatistics: async (league, season, team)=>{\n        const params = new URLSearchParams({\n            league: league.toString(),\n            season: season.toString(),\n            team: team.toString()\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/teams/statistics?\".concat(params.toString()));\n        return response;\n    },\n    // Helper methods for common operations\n    getTeamsByLeague: async (league, season)=>{\n        const filters = {\n            league\n        };\n        if (season) filters.season = season;\n        return teamsApi.getTeams(filters);\n    },\n    getTeamsByCountry: async (country)=>{\n        return teamsApi.getTeams({\n            country\n        });\n    },\n    searchTeams: async function(query) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        // Note: This would need to be implemented on the backend\n        // For now, we'll get all teams and filter client-side (not ideal for production)\n        const teams = await teamsApi.getTeams(filters);\n        const filteredTeams = teams.data.filter((team)=>{\n            var _team_code;\n            return team.name.toLowerCase().includes(query.toLowerCase()) || ((_team_code = team.code) === null || _team_code === void 0 ? void 0 : _team_code.toLowerCase().includes(query.toLowerCase()));\n        });\n        return {\n            data: filteredTeams,\n            meta: {\n                ...teams.meta,\n                totalItems: filteredTeams.length,\n                totalPages: Math.ceil(filteredTeams.length / (filters.limit || 10))\n            }\n        };\n    },\n    // Delete team (requires admin access)\n    deleteTeam: async (externalId)=>{\n        await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/football/teams/\".concat(externalId));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/teams.ts\n"));

/***/ })

});
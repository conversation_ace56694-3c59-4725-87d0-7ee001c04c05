"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/[id]/statistics/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/useTeams.ts":
/*!***********************************!*\
  !*** ./src/lib/hooks/useTeams.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSearchTeams: function() { return /* binding */ useSearchTeams; },\n/* harmony export */   useTeam: function() { return /* binding */ useTeam; },\n/* harmony export */   useTeamMutations: function() { return /* binding */ useTeamMutations; },\n/* harmony export */   useTeamStatistics: function() { return /* binding */ useTeamStatistics; },\n/* harmony export */   useTeams: function() { return /* binding */ useTeams; },\n/* harmony export */   useTeamsByCountry: function() { return /* binding */ useTeamsByCountry; },\n/* harmony export */   useTeamsByLeague: function() { return /* binding */ useTeamsByLeague; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n\n\nconst useTeams = function() {\n    let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _teamsQuery_data, _teamsQuery_data1;\n    const teamsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            filters\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeams(filters),\n        staleTime: 10 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsQuery_data = teamsQuery.data) === null || _teamsQuery_data === void 0 ? void 0 : _teamsQuery_data.data) || [],\n        teamsMeta: (_teamsQuery_data1 = teamsQuery.data) === null || _teamsQuery_data1 === void 0 ? void 0 : _teamsQuery_data1.meta,\n        isLoading: teamsQuery.isLoading,\n        error: teamsQuery.error,\n        refetch: teamsQuery.refetch\n    };\n};\nconst useTeam = (externalId)=>{\n    const teamQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"team\",\n            \"detail\",\n            externalId\n        ],\n        queryFn: async ()=>{\n            console.log(\"\\uD83D\\uDD04 useTeam - Fetching team with ID:\", externalId);\n            try {\n                const result = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamById(externalId);\n                console.log(\"✅ useTeam - Success:\", result);\n                return result;\n            } catch (error) {\n                console.error(\"❌ useTeam - Error:\", error);\n                throw error;\n            }\n        },\n        enabled: !!externalId && externalId > 0,\n        staleTime: 5 * 60 * 1000,\n        retry: (failureCount, error)=>{\n            console.log(\"\\uD83D\\uDD04 useTeam - Retry attempt:\", failureCount, error);\n            return failureCount < 3;\n        },\n        retryDelay: 1000\n    });\n    console.log(\"\\uD83D\\uDD0D useTeam - Query state:\", {\n        externalId,\n        data: teamQuery.data,\n        isLoading: teamQuery.isLoading,\n        error: teamQuery.error,\n        status: teamQuery.status,\n        fetchStatus: teamQuery.fetchStatus\n    });\n    return {\n        team: teamQuery.data,\n        isLoading: teamQuery.isLoading,\n        error: teamQuery.error,\n        refetch: teamQuery.refetch\n    };\n};\nconst useTeamsByLeague = (league, season)=>{\n    var _teamsByLeagueQuery_data, _teamsByLeagueQuery_data1;\n    const teamsByLeagueQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"by-league\",\n            league,\n            season\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamsByLeague(league, season),\n        enabled: !!league,\n        staleTime: 15 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsByLeagueQuery_data = teamsByLeagueQuery.data) === null || _teamsByLeagueQuery_data === void 0 ? void 0 : _teamsByLeagueQuery_data.data) || [],\n        teamsMeta: (_teamsByLeagueQuery_data1 = teamsByLeagueQuery.data) === null || _teamsByLeagueQuery_data1 === void 0 ? void 0 : _teamsByLeagueQuery_data1.meta,\n        isLoading: teamsByLeagueQuery.isLoading,\n        error: teamsByLeagueQuery.error,\n        refetch: teamsByLeagueQuery.refetch\n    };\n};\nconst useTeamsByCountry = (country)=>{\n    var _teamsByCountryQuery_data, _teamsByCountryQuery_data1;\n    const teamsByCountryQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"by-country\",\n            country\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamsByCountry(country),\n        enabled: !!country,\n        staleTime: 15 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsByCountryQuery_data = teamsByCountryQuery.data) === null || _teamsByCountryQuery_data === void 0 ? void 0 : _teamsByCountryQuery_data.data) || [],\n        teamsMeta: (_teamsByCountryQuery_data1 = teamsByCountryQuery.data) === null || _teamsByCountryQuery_data1 === void 0 ? void 0 : _teamsByCountryQuery_data1.meta,\n        isLoading: teamsByCountryQuery.isLoading,\n        error: teamsByCountryQuery.error,\n        refetch: teamsByCountryQuery.refetch\n    };\n};\nconst useTeamStatistics = (teamId)=>{\n    const statisticsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"statistics\",\n            teamId\n        ],\n        queryFn: ()=>{\n            // For now, return mock data since we need league and season info\n            // In real implementation, this would call teamsApi.getTeamStatistics with proper params\n            return Promise.resolve({\n                totalMatches: 28,\n                wins: 18,\n                draws: 6,\n                losses: 4,\n                goalsScored: 54,\n                goalsConceded: 23,\n                cleanSheets: 12,\n                winPercentage: 64.3,\n                avgGoalsPerMatch: 1.93,\n                avgGoalsConcededPerMatch: 0.82,\n                homeRecord: {\n                    wins: 11,\n                    draws: 3,\n                    losses: 0\n                },\n                awayRecord: {\n                    wins: 7,\n                    draws: 3,\n                    losses: 4\n                },\n                recentForm: [\n                    \"W\",\n                    \"W\",\n                    \"D\",\n                    \"W\",\n                    \"L\"\n                ]\n            });\n        },\n        enabled: !!teamId,\n        staleTime: 5 * 60 * 1000\n    });\n    return {\n        statistics: statisticsQuery.data,\n        isLoading: statisticsQuery.isLoading,\n        error: statisticsQuery.error,\n        refetch: statisticsQuery.refetch\n    };\n};\nconst useTeamMutations = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const deleteTeam = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (externalId)=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.deleteTeam(externalId),\n        onSuccess: ()=>{\n            // Invalidate and refetch teams data\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n        }\n    });\n    const syncTeam = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (externalId)=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.syncTeam(externalId),\n        onSuccess: (data, externalId)=>{\n            // Invalidate and refetch team data\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"team\",\n                    \"detail\",\n                    externalId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n            // Update the cache with new data\n            queryClient.setQueryData([\n                \"team\",\n                \"detail\",\n                externalId\n            ], data);\n        }\n    });\n    return {\n        deleteTeam: deleteTeam.mutate,\n        isDeleteLoading: deleteTeam.isLoading,\n        deleteError: deleteTeam.error,\n        syncTeam: syncTeam.mutate,\n        isSyncLoading: syncTeam.isLoading,\n        syncError: syncTeam.error\n    };\n};\nconst useSearchTeams = function(query) {\n    let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _searchQuery_data, _searchQuery_data1;\n    const searchQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"search\",\n            query,\n            filters\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.searchTeams(query, filters),\n        enabled: !!query && query.length >= 2,\n        staleTime: 2 * 60 * 1000\n    });\n    return {\n        searchResults: ((_searchQuery_data = searchQuery.data) === null || _searchQuery_data === void 0 ? void 0 : _searchQuery_data.data) || [],\n        searchMeta: (_searchQuery_data1 = searchQuery.data) === null || _searchQuery_data1 === void 0 ? void 0 : _searchQuery_data1.meta,\n        isSearchLoading: searchQuery.isLoading,\n        searchError: searchQuery.error,\n        refetchSearch: searchQuery.refetch\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/useTeams.ts\n"));

/***/ })

});
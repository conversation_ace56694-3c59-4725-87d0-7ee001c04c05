"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/api/teams.ts":
/*!******************************!*\
  !*** ./src/lib/api/teams.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   teamsApi: function() { return /* binding */ teamsApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\n// Helper function to get auth headers\nconst getAuthHeaders = ()=>{\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    if (true) {\n        try {\n            const authStorage = localStorage.getItem(\"auth-storage\");\n            if (authStorage) {\n                var _parsed_state;\n                const parsed = JSON.parse(authStorage);\n                const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                if (token) {\n                    headers.Authorization = \"Bearer \".concat(token);\n                    return headers;\n                }\n            }\n        } catch (error) {\n            console.warn(\"Failed to parse auth storage:\", error);\n        }\n        // Fallback to direct localStorage access\n        const fallbackToken = localStorage.getItem(\"accessToken\");\n        if (fallbackToken) {\n            headers.Authorization = \"Bearer \".concat(fallbackToken);\n            return headers;\n        }\n    }\n    return headers;\n};\nconst teamsApi = {\n    // Use Next.js API proxy (similar to leagues)\n    getTeams: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await fetch(\"/api/teams?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch teams\");\n        }\n        return await response.json();\n    },\n    // Use Next.js API proxy for team details\n    getTeamById: async (externalId)=>{\n        const response = await fetch(\"/api/teams/\".concat(externalId), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch team details\");\n        }\n        const data = await response.json();\n        return data.data; // Extract team data from API response\n    },\n    // Requires authentication\n    getTeamStatistics: async (league, season, team)=>{\n        const params = new URLSearchParams({\n            league: league.toString(),\n            season: season.toString(),\n            team: team.toString()\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/teams/statistics?\".concat(params.toString()));\n        return response;\n    },\n    // Helper methods for common operations\n    getTeamsByLeague: async (league, season)=>{\n        const filters = {\n            league\n        };\n        if (season) filters.season = season;\n        return teamsApi.getTeams(filters);\n    },\n    getTeamsByCountry: async (country)=>{\n        return teamsApi.getTeams({\n            country\n        });\n    },\n    searchTeams: async function(query) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        // Note: This would need to be implemented on the backend\n        // For now, we'll get all teams and filter client-side (not ideal for production)\n        const teams = await teamsApi.getTeams(filters);\n        const filteredTeams = teams.data.filter((team)=>{\n            var _team_code;\n            return team.name.toLowerCase().includes(query.toLowerCase()) || ((_team_code = team.code) === null || _team_code === void 0 ? void 0 : _team_code.toLowerCase().includes(query.toLowerCase()));\n        });\n        return {\n            data: filteredTeams,\n            meta: {\n                ...teams.meta,\n                totalItems: filteredTeams.length,\n                totalPages: Math.ceil(filteredTeams.length / (filters.limit || 10))\n            }\n        };\n    },\n    // Sync team data from external source (requires authentication)\n    syncTeam: async (externalId)=>{\n        const response = await fetch(\"/api/teams/\".concat(externalId, \"?newdb=true\"), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to sync team data\");\n        }\n        const data = await response.json();\n        return data.data; // Extract team data from API response\n    },\n    // Delete team (requires admin access)\n    deleteTeam: async (externalId)=>{\n        await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/football/teams/\".concat(externalId));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/teams.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/useTeams.ts":
/*!***********************************!*\
  !*** ./src/lib/hooks/useTeams.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSearchTeams: function() { return /* binding */ useSearchTeams; },\n/* harmony export */   useTeam: function() { return /* binding */ useTeam; },\n/* harmony export */   useTeamLeaguesSeasons: function() { return /* binding */ useTeamLeaguesSeasons; },\n/* harmony export */   useTeamMutations: function() { return /* binding */ useTeamMutations; },\n/* harmony export */   useTeamStatistics: function() { return /* binding */ useTeamStatistics; },\n/* harmony export */   useTeams: function() { return /* binding */ useTeams; },\n/* harmony export */   useTeamsByCountry: function() { return /* binding */ useTeamsByCountry; },\n/* harmony export */   useTeamsByLeague: function() { return /* binding */ useTeamsByLeague; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n\n\nconst useTeams = function() {\n    let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _teamsQuery_data, _teamsQuery_data1;\n    const teamsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            filters\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeams(filters),\n        staleTime: 10 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsQuery_data = teamsQuery.data) === null || _teamsQuery_data === void 0 ? void 0 : _teamsQuery_data.data) || [],\n        teamsMeta: (_teamsQuery_data1 = teamsQuery.data) === null || _teamsQuery_data1 === void 0 ? void 0 : _teamsQuery_data1.meta,\n        isLoading: teamsQuery.isLoading,\n        error: teamsQuery.error,\n        refetch: teamsQuery.refetch\n    };\n};\nconst useTeam = (externalId)=>{\n    const teamQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"team\",\n            \"detail\",\n            externalId\n        ],\n        queryFn: async ()=>{\n            console.log(\"\\uD83D\\uDD04 useTeam - Fetching team with ID:\", externalId);\n            try {\n                const result = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamById(externalId);\n                console.log(\"✅ useTeam - Success:\", result);\n                return result;\n            } catch (error) {\n                console.error(\"❌ useTeam - Error:\", error);\n                throw error;\n            }\n        },\n        enabled: !!externalId && externalId > 0,\n        staleTime: 5 * 60 * 1000,\n        retry: (failureCount, error)=>{\n            console.log(\"\\uD83D\\uDD04 useTeam - Retry attempt:\", failureCount, error);\n            return failureCount < 3;\n        },\n        retryDelay: 1000\n    });\n    console.log(\"\\uD83D\\uDD0D useTeam - Query state:\", {\n        externalId,\n        data: teamQuery.data,\n        isLoading: teamQuery.isLoading,\n        error: teamQuery.error,\n        status: teamQuery.status,\n        fetchStatus: teamQuery.fetchStatus\n    });\n    return {\n        team: teamQuery.data,\n        isLoading: teamQuery.isLoading,\n        error: teamQuery.error,\n        refetch: teamQuery.refetch\n    };\n};\nconst useTeamsByLeague = (league, season)=>{\n    var _teamsByLeagueQuery_data, _teamsByLeagueQuery_data1;\n    const teamsByLeagueQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"by-league\",\n            league,\n            season\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamsByLeague(league, season),\n        enabled: !!league,\n        staleTime: 15 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsByLeagueQuery_data = teamsByLeagueQuery.data) === null || _teamsByLeagueQuery_data === void 0 ? void 0 : _teamsByLeagueQuery_data.data) || [],\n        teamsMeta: (_teamsByLeagueQuery_data1 = teamsByLeagueQuery.data) === null || _teamsByLeagueQuery_data1 === void 0 ? void 0 : _teamsByLeagueQuery_data1.meta,\n        isLoading: teamsByLeagueQuery.isLoading,\n        error: teamsByLeagueQuery.error,\n        refetch: teamsByLeagueQuery.refetch\n    };\n};\nconst useTeamsByCountry = (country)=>{\n    var _teamsByCountryQuery_data, _teamsByCountryQuery_data1;\n    const teamsByCountryQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"by-country\",\n            country\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamsByCountry(country),\n        enabled: !!country,\n        staleTime: 15 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsByCountryQuery_data = teamsByCountryQuery.data) === null || _teamsByCountryQuery_data === void 0 ? void 0 : _teamsByCountryQuery_data.data) || [],\n        teamsMeta: (_teamsByCountryQuery_data1 = teamsByCountryQuery.data) === null || _teamsByCountryQuery_data1 === void 0 ? void 0 : _teamsByCountryQuery_data1.meta,\n        isLoading: teamsByCountryQuery.isLoading,\n        error: teamsByCountryQuery.error,\n        refetch: teamsByCountryQuery.refetch\n    };\n};\nconst useTeamStatistics = (teamId)=>{\n    const statisticsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"statistics\",\n            teamId\n        ],\n        queryFn: ()=>{\n            // For now, return mock data since we need league and season info\n            // In real implementation, this would call teamsApi.getTeamStatistics with proper params\n            return Promise.resolve({\n                totalMatches: 28,\n                wins: 18,\n                draws: 6,\n                losses: 4,\n                goalsScored: 54,\n                goalsConceded: 23,\n                cleanSheets: 12,\n                winPercentage: 64.3,\n                avgGoalsPerMatch: 1.93,\n                avgGoalsConcededPerMatch: 0.82,\n                homeRecord: {\n                    wins: 11,\n                    draws: 3,\n                    losses: 0\n                },\n                awayRecord: {\n                    wins: 7,\n                    draws: 3,\n                    losses: 4\n                },\n                recentForm: [\n                    \"W\",\n                    \"W\",\n                    \"D\",\n                    \"W\",\n                    \"L\"\n                ]\n            });\n        },\n        enabled: !!teamId,\n        staleTime: 5 * 60 * 1000\n    });\n    return {\n        statistics: statisticsQuery.data,\n        isLoading: statisticsQuery.isLoading,\n        error: statisticsQuery.error,\n        refetch: statisticsQuery.refetch\n    };\n};\nconst useTeamMutations = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const deleteTeam = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (externalId)=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.deleteTeam(externalId),\n        onSuccess: ()=>{\n            // Invalidate and refetch teams data\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n        }\n    });\n    const syncTeam = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (externalId)=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.syncTeam(externalId),\n        onSuccess: (data, externalId)=>{\n            // Invalidate and refetch team data\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"team\",\n                    \"detail\",\n                    externalId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n            // Update the cache with new data\n            queryClient.setQueryData([\n                \"team\",\n                \"detail\",\n                externalId\n            ], data);\n        }\n    });\n    return {\n        deleteTeam: deleteTeam.mutate,\n        isDeleteLoading: deleteTeam.isLoading,\n        deleteError: deleteTeam.error,\n        syncTeam: syncTeam.mutate,\n        isSyncLoading: syncTeam.isLoading,\n        syncError: syncTeam.error\n    };\n};\nconst useTeamLeaguesSeasons = function(externalId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"by-league\";\n    const leaguesSeasonsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"team\",\n            \"leagues-seasons\",\n            externalId,\n            format\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamLeaguesSeasons(externalId, format),\n        enabled: !!externalId && externalId > 0,\n        staleTime: 10 * 60 * 1000\n    });\n    return {\n        leaguesSeasons: leaguesSeasonsQuery.data,\n        isLoading: leaguesSeasonsQuery.isLoading,\n        error: leaguesSeasonsQuery.error,\n        refetch: leaguesSeasonsQuery.refetch\n    };\n};\nconst useSearchTeams = function(query) {\n    let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _searchQuery_data, _searchQuery_data1;\n    const searchQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"search\",\n            query,\n            filters\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.searchTeams(query, filters),\n        enabled: !!query && query.length >= 2,\n        staleTime: 2 * 60 * 1000\n    });\n    return {\n        searchResults: ((_searchQuery_data = searchQuery.data) === null || _searchQuery_data === void 0 ? void 0 : _searchQuery_data.data) || [],\n        searchMeta: (_searchQuery_data1 = searchQuery.data) === null || _searchQuery_data1 === void 0 ? void 0 : _searchQuery_data1.meta,\n        isSearchLoading: searchQuery.isLoading,\n        searchError: searchQuery.error,\n        refetchSearch: searchQuery.refetch\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/useTeams.ts\n"));

/***/ })

});
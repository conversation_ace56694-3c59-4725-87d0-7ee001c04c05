"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/teams/[id]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/teams/[id]/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TeamDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useTeams */ \"(app-pages-browser)/./src/lib/hooks/useTeams.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _lib_utils_image__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/image */ \"(app-pages-browser)/./src/lib/utils/image.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,RefreshCw,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TeamDetailPage() {\n    var _team_venue_capacity;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { isEditor, isAdmin } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions)();\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQueryClient)();\n    const teamId = parseInt(params.id);\n    // Fetch team details\n    const { team, isLoading, error, refetch } = (0,_lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__.useTeam)(teamId);\n    // Team mutations\n    const { syncTeam, isSyncLoading, syncError } = (0,_lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__.useTeamMutations)();\n    // Debug logging\n    console.log(\"\\uD83D\\uDD0D Team Details Debug:\", {\n        teamId,\n        team,\n        isLoading,\n        error: error instanceof Error ? error.message : error,\n        hasTeam: !!team\n    });\n    // Force refetch on mount if no data\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!isLoading && !team && !error) {\n            console.log(\"\\uD83D\\uDD04 Force refetch team data\");\n            refetch();\n        }\n    }, [\n        teamId,\n        isLoading,\n        team,\n        error,\n        refetch\n    ]);\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.deleteTeam(teamId),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"Team deleted successfully\");\n            setDeleteModalOpen(false);\n            router.push(\"/dashboard/teams\");\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_12__.toast.error(error.message || \"Failed to delete team\");\n            setDeleteModalOpen(false);\n        }\n    });\n    // Handlers\n    const handleEdit = ()=>{\n        router.push(\"/dashboard/teams/\".concat(teamId, \"/edit\"));\n    };\n    const handleDelete = ()=>{\n        setDeleteModalOpen(true);\n    };\n    const handleViewStatistics = ()=>{\n        router.push(\"/dashboard/teams/\".concat(teamId, \"/statistics\"));\n    };\n    const handleSync = ()=>{\n        syncTeam(teamId, {\n            onSuccess: ()=>{\n                sonner__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"Team data synced successfully\");\n                refetch(); // Refresh the team data\n            },\n            onError: (error)=>{\n                sonner__WEBPACK_IMPORTED_MODULE_12__.toast.error(error.message || \"Failed to sync team data\");\n            }\n        });\n    };\n    const confirmDelete = ()=>{\n        deleteMutation.mutate();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-8 w-48\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 25\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                className: \"h-96\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n            lineNumber: 114,\n            columnNumber: 19\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 37\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 31\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 25\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border-red-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"flex items-center justify-center h-96\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-12 h-12 text-red-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-red-900 mb-2\",\n                                    children: \"Error loading team\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: error instanceof Error ? error.message : \"Unknown error occurred\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 43\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 37\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 31\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n            lineNumber: 135,\n            columnNumber: 19\n        }, this);\n    }\n    if (!team) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 37\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 31\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 25\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"flex items-center justify-center h-96\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"Team not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mb-4\",\n                                    children: \"The team you're looking for doesn't exist or you don't have permission to view it.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 43\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 37\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 31\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n            lineNumber: 169,\n            columnNumber: 19\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.back(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 37\n                                    }, this),\n                                    \"Back\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) || \"\",\n                                        alt: team.name,\n                                        className: \"w-12 h-12 object-contain rounded-full\",\n                                        onError: (e)=>{\n                                            const target = e.target;\n                                            target.style.display = \"none\";\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 43\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: team.name\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 text-gray-600\",\n                                                children: [\n                                                    team.country ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildCountryFlagUrl)(team.country) || \"\",\n                                                                alt: \"\".concat(team.country, \" flag\"),\n                                                                className: \"w-4 h-3 object-cover\",\n                                                                onError: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.display = \"none\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: team.country\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 italic\",\n                                                        children: \"Country not available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 55\n                                                    }, this),\n                                                    team.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-mono text-sm\",\n                                                                children: team.code\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    team.national && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs\",\n                                                                children: \"National Team\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 43\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 31\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleViewStatistics,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 37\n                                    }, this),\n                                    \"Statistics\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 31\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleSync,\n                                disabled: isSyncLoading,\n                                className: \"text-blue-600 hover:text-blue-700 hover:bg-blue-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2 \".concat(isSyncLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 43\n                                    }, this),\n                                    isSyncLoading ? \"Syncing...\" : \"Sync Data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 37\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleEdit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 37\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleDelete,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                lineNumber: 204,\n                columnNumber: 19\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 49\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Team Information\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 49\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 43\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Team Name\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium mt-1\",\n                                                            children: team.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Team Code\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.code ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium font-mono mt-1\",\n                                                            children: team.code\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Country\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.country ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildCountryFlagUrl)(team.country) || \"\",\n                                                                    alt: \"\".concat(team.country, \" flag\"),\n                                                                    className: \"w-6 h-4 object-cover\",\n                                                                    onError: (e)=>{\n                                                                        const target = e.target;\n                                                                        target.style.display = \"none\";\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-medium\",\n                                                                    children: team.country\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Founded\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.founded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium mt-1\",\n                                                            children: team.founded\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Team Type\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                            children: team.national ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"default\",\n                                                                className: \"bg-blue-100 text-blue-800\",\n                                                                children: \"National Team\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 67\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: \"Club Team\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 67\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Home Venue\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.venue ? typeof team.venue === \"string\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium mt-1\",\n                                                            children: team.venue\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 67\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-medium\",\n                                                                    children: team.venue.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 73\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: team.venue.city\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 73\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Capacity: \",\n                                                                        (_team_venue_capacity = team.venue.capacity) === null || _team_venue_capacity === void 0 ? void 0 : _team_venue_capacity.toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 73\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 67\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 43\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-500 mb-4 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        \"System Information\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"External ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        team.externalId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"Internal ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        team.id\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.season && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"Season\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: team.season\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 61\n                                                        }, this),\n                                                        team.leagueId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"League ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        team.leagueId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 43\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 31\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 49\n                                                }, this),\n                                                \"Team Logo\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 43\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) || \"\",\n                                                    alt: team.name,\n                                                    className: \"w-32 h-32 object-contain\",\n                                                    onError: (e)=>{\n                                                        const target = e.target;\n                                                        target.src = \"\";\n                                                        target.style.display = \"none\";\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 55\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-32 h-32 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-16 h-16 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 55\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 43\n                                            }, this),\n                                            team.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 break-all\",\n                                                    children: team.logo\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 55\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 49\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 49\n                                                }, this),\n                                                \"Quick Info\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 43\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Country\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: team.country ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildCountryFlagUrl)(team.country) || \"\",\n                                                                    alt: \"\".concat(team.country, \" flag\"),\n                                                                    className: \"w-4 h-3 object-cover\",\n                                                                    onError: (e)=>{\n                                                                        const target = e.target;\n                                                                        target.style.display = \"none\";\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: team.country\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-400 italic\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Founded\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    team.founded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"font-mono\",\n                                                        children: team.founded\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 55\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-400 italic\",\n                                                        children: \"Not available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Code\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    team.code ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"font-mono\",\n                                                        children: team.code\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 55\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-400 italic\",\n                                                        children: \"Not available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Type\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    team.national ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        className: \"bg-blue-100 text-blue-800\",\n                                                        children: \"National\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 55\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: \"Club\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Team ID\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium font-mono\",\n                                                        children: [\n                                                            \"#\",\n                                                            team.externalId\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 43\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 31\n                            }, this),\n                            (team.venue || team.season || team.leagueId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 55\n                                                }, this),\n                                                \"Additional Info\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            team.venue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            \"Venue\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: team.venue\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 55\n                                            }, this),\n                                            team.season && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            \"Season\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"font-mono\",\n                                                        children: team.season\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 55\n                                            }, this),\n                                            team.leagueId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            \"League ID\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium font-mono\",\n                                                        children: [\n                                                            \"#\",\n                                                            team.leagueId\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 55\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 43\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                lineNumber: 319,\n                columnNumber: 19\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>setDeleteModalOpen(false),\n                title: \"Delete Team\",\n                description: \"Are you sure you want to delete this team? This action cannot be undone.\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_RefreshCw_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-600 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"This will permanently delete the team:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 43\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: team.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 49\n                                                }, this),\n                                                \" \",\n                                                team.country ? \"(\".concat(team.country, \")\") : \"(Country not available)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 43\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteModalOpen(false),\n                                    disabled: deleteMutation.isLoading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDelete,\n                                    disabled: deleteMutation.isLoading,\n                                    children: deleteMutation.isLoading ? \"Deleting...\" : \"Delete Team\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 647,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                lineNumber: 641,\n                columnNumber: 19\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n        lineNumber: 202,\n        columnNumber: 13\n    }, this);\n}\n_s(TeamDetailPage, \"zrxRjURWAlLPY7yoTcc6SRjRvoQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQueryClient,\n        _lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__.useTeam,\n        _lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__.useTeamMutations,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = TeamDetailPage;\nvar _c;\n$RefreshReg$(_c, \"TeamDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/teams/[id]/page.tsx\n"));

/***/ })

});
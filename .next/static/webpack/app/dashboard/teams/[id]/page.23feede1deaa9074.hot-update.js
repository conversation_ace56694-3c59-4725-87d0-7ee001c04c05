"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/teams/[id]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/teams/[id]/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TeamDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useTeams */ \"(app-pages-browser)/./src/lib/hooks/useTeams.ts\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n/* harmony import */ var _lib_utils_image__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/image */ \"(app-pages-browser)/./src/lib/utils/image.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,BarChart3,Building,Calendar,Edit,ExternalLink,Flag,Hash,Home,Info,Shield,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TeamDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { isEditor, isAdmin } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions)();\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQueryClient)();\n    const teamId = parseInt(params.id);\n    // Fetch team details\n    const { team, isLoading, error, refetch } = (0,_lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__.useTeam)(teamId);\n    // Debug logging\n    console.log(\"\\uD83D\\uDD0D Team Details Debug:\", {\n        teamId,\n        team,\n        isLoading,\n        error: error instanceof Error ? error.message : error,\n        hasTeam: !!team\n    });\n    // Force refetch on mount if no data\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!isLoading && !team && !error) {\n            console.log(\"\\uD83D\\uDD04 Force refetch team data\");\n            refetch();\n        }\n    }, [\n        teamId,\n        isLoading,\n        team,\n        error,\n        refetch\n    ]);\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation)({\n        mutationFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_10__.teamsApi.deleteTeam(teamId),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"Team deleted successfully\");\n            setDeleteModalOpen(false);\n            router.push(\"/dashboard/teams\");\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_12__.toast.error(error.message || \"Failed to delete team\");\n            setDeleteModalOpen(false);\n        }\n    });\n    // Handlers\n    const handleEdit = ()=>{\n        router.push(\"/dashboard/teams/\".concat(teamId, \"/edit\"));\n    };\n    const handleDelete = ()=>{\n        setDeleteModalOpen(true);\n    };\n    const handleViewStatistics = ()=>{\n        router.push(\"/dashboard/teams/\".concat(teamId, \"/statistics\"));\n    };\n    const confirmDelete = ()=>{\n        deleteMutation.mutate();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-8 w-48\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 25\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                className: \"h-96\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n            lineNumber: 98,\n            columnNumber: 19\n        }, this);\n    }\n    if (error || !team) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 37\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 31\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 25\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"flex items-center justify-center h-96\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"Team not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"The team you're looking for doesn't exist or you don't have permission to view it.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 43\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 37\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 31\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n            lineNumber: 119,\n            columnNumber: 19\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.back(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 37\n                                    }, this),\n                                    \"Back\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) || \"\",\n                                        alt: team.name,\n                                        className: \"w-12 h-12 object-contain rounded-full\",\n                                        onError: (e)=>{\n                                            const target = e.target;\n                                            target.style.display = \"none\";\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 43\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: team.name\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 text-gray-600\",\n                                                children: [\n                                                    team.country ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildCountryFlagUrl)(team.country) || \"\",\n                                                                alt: \"\".concat(team.country, \" flag\"),\n                                                                className: \"w-4 h-3 object-cover\",\n                                                                onError: (e)=>{\n                                                                    const target = e.target;\n                                                                    target.style.display = \"none\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: team.country\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 italic\",\n                                                        children: \"Country not available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 55\n                                                    }, this),\n                                                    team.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-mono text-sm\",\n                                                                children: team.code\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    team.national && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs\",\n                                                                children: \"National Team\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 43\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 31\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleViewStatistics,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 37\n                                    }, this),\n                                    \"Statistics\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 31\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleEdit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 37\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleDelete,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                lineNumber: 151,\n                columnNumber: 19\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 49\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Team Information\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 49\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 43\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Team Name\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium mt-1\",\n                                                            children: team.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Team Code\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.code ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium font-mono mt-1\",\n                                                            children: team.code\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Country\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.country ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildCountryFlagUrl)(team.country) || \"\",\n                                                                    alt: \"\".concat(team.country, \" flag\"),\n                                                                    className: \"w-6 h-4 object-cover\",\n                                                                    onError: (e)=>{\n                                                                        const target = e.target;\n                                                                        target.style.display = \"none\";\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-medium\",\n                                                                    children: team.country\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Founded\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.founded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium mt-1\",\n                                                            children: team.founded\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Team Type\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                            children: team.national ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"default\",\n                                                                className: \"bg-blue-100 text-blue-800\",\n                                                                children: \"National Team\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 67\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: \"Club Team\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 67\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                \"Home Venue\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.venue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium mt-1\",\n                                                            children: team.venue\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 61\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 italic mt-1\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 43\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-500 mb-4 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        \"System Information\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"External ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        team.externalId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"Internal ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        team.id\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        team.season && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"Season\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: team.season\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 61\n                                                        }, this),\n                                                        team.leagueId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs font-medium text-gray-400\",\n                                                                    children: \"League ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        team.leagueId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 43\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 31\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 49\n                                                }, this),\n                                                \"Team Logo\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 43\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildTeamLogoUrl)(team.logo) || \"\",\n                                                    alt: team.name,\n                                                    className: \"w-32 h-32 object-contain\",\n                                                    onError: (e)=>{\n                                                        const target = e.target;\n                                                        target.src = \"\";\n                                                        target.style.display = \"none\";\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 55\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-32 h-32 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-16 h-16 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 55\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 43\n                                            }, this),\n                                            team.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 break-all\",\n                                                    children: team.logo\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 55\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 49\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 49\n                                                }, this),\n                                                \"Quick Info\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 43\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Country\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: team.country ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_11__.buildCountryFlagUrl)(team.country) || \"\",\n                                                                    alt: \"\".concat(team.country, \" flag\"),\n                                                                    className: \"w-4 h-3 object-cover\",\n                                                                    onError: (e)=>{\n                                                                        const target = e.target;\n                                                                        target.style.display = \"none\";\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: team.country\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 67\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-400 italic\",\n                                                            children: \"Not available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 61\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Founded\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    team.founded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"font-mono\",\n                                                        children: team.founded\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 55\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-400 italic\",\n                                                        children: \"Not available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Code\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    team.code ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"font-mono\",\n                                                        children: team.code\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 55\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-400 italic\",\n                                                        children: \"Not available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Type\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    team.national ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        className: \"bg-blue-100 text-blue-800\",\n                                                        children: \"National\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 55\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: \"Club\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 55\n                                                            }, this),\n                                                            \"Team ID\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium font-mono\",\n                                                        children: [\n                                                            \"#\",\n                                                            team.externalId\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 43\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 31\n                            }, this),\n                            (team.venue || team.season || team.leagueId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 55\n                                                }, this),\n                                                \"Additional Info\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            team.venue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            \"Venue\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: team.venue\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 55\n                                            }, this),\n                                            team.season && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            \"Season\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"font-mono\",\n                                                        children: team.season\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 55\n                                            }, this),\n                                            team.leagueId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            \"League ID\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium font-mono\",\n                                                        children: [\n                                                            \"#\",\n                                                            team.leagueId\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 55\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 43\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                lineNumber: 253,\n                columnNumber: 19\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>setDeleteModalOpen(false),\n                title: \"Delete Team\",\n                description: \"Are you sure you want to delete this team? This action cannot be undone.\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_BarChart3_Building_Calendar_Edit_ExternalLink_Flag_Hash_Home_Info_Shield_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-600 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"This will permanently delete the team:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 43\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: team.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 49\n                                                }, this),\n                                                \" \",\n                                                team.country ? \"(\".concat(team.country, \")\") : \"(Country not available)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 43\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteModalOpen(false),\n                                    disabled: deleteMutation.isLoading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDelete,\n                                    disabled: deleteMutation.isLoading,\n                                    children: deleteMutation.isLoading ? \"Deleting...\" : \"Delete Team\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n                lineNumber: 567,\n                columnNumber: 19\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/teams/[id]/page.tsx\",\n        lineNumber: 149,\n        columnNumber: 13\n    }, this);\n}\n_s(TeamDetailPage, \"XbzTRipxG32HRlwiB8JjPMqruqs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQueryClient,\n        _lib_hooks_useTeams__WEBPACK_IMPORTED_MODULE_9__.useTeam,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useMutation\n    ];\n});\n_c = TeamDetailPage;\nvar _c;\n$RefreshReg$(_c, \"TeamDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/teams/[id]/page.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/page",{

/***/ "(app-pages-browser)/./src/lib/api/teams.ts":
/*!******************************!*\
  !*** ./src/lib/api/teams.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   teamsApi: function() { return /* binding */ teamsApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\n// Helper function to get auth headers\nconst getAuthHeaders = ()=>{\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    if (true) {\n        try {\n            const authStorage = localStorage.getItem(\"auth-storage\");\n            console.log(\"\\uD83D\\uDD11 Teams API - Auth storage:\", authStorage ? \"Found\" : \"Not found\");\n            if (authStorage) {\n                var _parsed_state;\n                const parsed = JSON.parse(authStorage);\n                const token = (_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken;\n                console.log(\"\\uD83D\\uDD11 Teams API - Token:\", token ? \"Found (\".concat(token.substring(0, 20), \"...)\") : \"Not found\");\n                if (token) {\n                    headers.Authorization = \"Bearer \".concat(token);\n                    return headers;\n                }\n            }\n        } catch (error) {\n            console.warn(\"Failed to parse auth storage:\", error);\n        }\n        // Fallback to direct localStorage access\n        const fallbackToken = localStorage.getItem(\"accessToken\");\n        console.log(\"\\uD83D\\uDD11 Teams API - Fallback token:\", fallbackToken ? \"Found (\".concat(fallbackToken.substring(0, 20), \"...)\") : \"Not found\");\n        if (fallbackToken) {\n            headers.Authorization = \"Bearer \".concat(fallbackToken);\n            return headers;\n        }\n    }\n    console.log(\"\\uD83D\\uDD11 Teams API - Final headers:\", headers);\n    return headers;\n};\nconst teamsApi = {\n    // Use Next.js API proxy (similar to leagues)\n    getTeams: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await fetch(\"/api/teams?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch teams\");\n        }\n        return await response.json();\n    },\n    // Use Next.js API proxy for team details\n    getTeamById: async (externalId)=>{\n        const response = await fetch(\"/api/teams/\".concat(externalId), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch team details\");\n        }\n        const data = await response.json();\n        return data.data; // Extract team data from API response\n    },\n    // Requires authentication\n    getTeamStatistics: async (league, season, team)=>{\n        const params = new URLSearchParams({\n            league: league.toString(),\n            season: season.toString(),\n            team: team.toString()\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/teams/statistics?\".concat(params.toString()));\n        return response;\n    },\n    // Helper methods for common operations\n    getTeamsByLeague: async (league, season)=>{\n        const filters = {\n            league\n        };\n        if (season) filters.season = season;\n        return teamsApi.getTeams(filters);\n    },\n    getTeamsByCountry: async (country)=>{\n        return teamsApi.getTeams({\n            country\n        });\n    },\n    searchTeams: async function(query) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        // Note: This would need to be implemented on the backend\n        // For now, we'll get all teams and filter client-side (not ideal for production)\n        const teams = await teamsApi.getTeams(filters);\n        const filteredTeams = teams.data.filter((team)=>{\n            var _team_code;\n            return team.name.toLowerCase().includes(query.toLowerCase()) || ((_team_code = team.code) === null || _team_code === void 0 ? void 0 : _team_code.toLowerCase().includes(query.toLowerCase()));\n        });\n        return {\n            data: filteredTeams,\n            meta: {\n                ...teams.meta,\n                totalItems: filteredTeams.length,\n                totalPages: Math.ceil(filteredTeams.length / (filters.limit || 10))\n            }\n        };\n    },\n    // Delete team (requires admin access)\n    deleteTeam: async (externalId)=>{\n        await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"/football/teams/\".concat(externalId));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/teams.ts\n"));

/***/ })

});
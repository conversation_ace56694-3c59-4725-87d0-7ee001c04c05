"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/useTeams.ts":
/*!***********************************!*\
  !*** ./src/lib/hooks/useTeams.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSearchTeams: function() { return /* binding */ useSearchTeams; },\n/* harmony export */   useTeam: function() { return /* binding */ useTeam; },\n/* harmony export */   useTeamMutations: function() { return /* binding */ useTeamMutations; },\n/* harmony export */   useTeamStatistics: function() { return /* binding */ useTeamStatistics; },\n/* harmony export */   useTeams: function() { return /* binding */ useTeams; },\n/* harmony export */   useTeamsByCountry: function() { return /* binding */ useTeamsByCountry; },\n/* harmony export */   useTeamsByLeague: function() { return /* binding */ useTeamsByLeague; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n\n\nconst useTeams = function() {\n    let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _teamsQuery_data, _teamsQuery_data1;\n    const teamsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            filters\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeams(filters),\n        staleTime: 10 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsQuery_data = teamsQuery.data) === null || _teamsQuery_data === void 0 ? void 0 : _teamsQuery_data.data) || [],\n        teamsMeta: (_teamsQuery_data1 = teamsQuery.data) === null || _teamsQuery_data1 === void 0 ? void 0 : _teamsQuery_data1.meta,\n        isLoading: teamsQuery.isLoading,\n        error: teamsQuery.error,\n        refetch: teamsQuery.refetch\n    };\n};\nconst useTeam = (externalId)=>{\n    const teamQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"team\",\n            \"detail\",\n            externalId\n        ],\n        queryFn: ()=>{\n            console.log(\"\\uD83D\\uDD04 useTeam - Fetching team with ID:\", externalId);\n            return _lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamById(externalId);\n        },\n        enabled: !!externalId,\n        staleTime: 10 * 60 * 1000,\n        retry: 3,\n        retryDelay: 1000\n    });\n    console.log(\"\\uD83D\\uDD0D useTeam - Query state:\", {\n        externalId,\n        data: teamQuery.data,\n        isLoading: teamQuery.isLoading,\n        error: teamQuery.error,\n        status: teamQuery.status\n    });\n    return {\n        team: teamQuery.data,\n        isLoading: teamQuery.isLoading,\n        error: teamQuery.error,\n        refetch: teamQuery.refetch\n    };\n};\nconst useTeamsByLeague = (league, season)=>{\n    var _teamsByLeagueQuery_data, _teamsByLeagueQuery_data1;\n    const teamsByLeagueQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"by-league\",\n            league,\n            season\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamsByLeague(league, season),\n        enabled: !!league,\n        staleTime: 15 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsByLeagueQuery_data = teamsByLeagueQuery.data) === null || _teamsByLeagueQuery_data === void 0 ? void 0 : _teamsByLeagueQuery_data.data) || [],\n        teamsMeta: (_teamsByLeagueQuery_data1 = teamsByLeagueQuery.data) === null || _teamsByLeagueQuery_data1 === void 0 ? void 0 : _teamsByLeagueQuery_data1.meta,\n        isLoading: teamsByLeagueQuery.isLoading,\n        error: teamsByLeagueQuery.error,\n        refetch: teamsByLeagueQuery.refetch\n    };\n};\nconst useTeamsByCountry = (country)=>{\n    var _teamsByCountryQuery_data, _teamsByCountryQuery_data1;\n    const teamsByCountryQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"by-country\",\n            country\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamsByCountry(country),\n        enabled: !!country,\n        staleTime: 15 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsByCountryQuery_data = teamsByCountryQuery.data) === null || _teamsByCountryQuery_data === void 0 ? void 0 : _teamsByCountryQuery_data.data) || [],\n        teamsMeta: (_teamsByCountryQuery_data1 = teamsByCountryQuery.data) === null || _teamsByCountryQuery_data1 === void 0 ? void 0 : _teamsByCountryQuery_data1.meta,\n        isLoading: teamsByCountryQuery.isLoading,\n        error: teamsByCountryQuery.error,\n        refetch: teamsByCountryQuery.refetch\n    };\n};\nconst useTeamStatistics = (teamId)=>{\n    const statisticsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"statistics\",\n            teamId\n        ],\n        queryFn: ()=>{\n            // For now, return mock data since we need league and season info\n            // In real implementation, this would call teamsApi.getTeamStatistics with proper params\n            return Promise.resolve({\n                totalMatches: 28,\n                wins: 18,\n                draws: 6,\n                losses: 4,\n                goalsScored: 54,\n                goalsConceded: 23,\n                cleanSheets: 12,\n                winPercentage: 64.3,\n                avgGoalsPerMatch: 1.93,\n                avgGoalsConcededPerMatch: 0.82,\n                homeRecord: {\n                    wins: 11,\n                    draws: 3,\n                    losses: 0\n                },\n                awayRecord: {\n                    wins: 7,\n                    draws: 3,\n                    losses: 4\n                },\n                recentForm: [\n                    \"W\",\n                    \"W\",\n                    \"D\",\n                    \"W\",\n                    \"L\"\n                ]\n            });\n        },\n        enabled: !!teamId,\n        staleTime: 5 * 60 * 1000\n    });\n    return {\n        statistics: statisticsQuery.data,\n        isLoading: statisticsQuery.isLoading,\n        error: statisticsQuery.error,\n        refetch: statisticsQuery.refetch\n    };\n};\nconst useTeamMutations = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const deleteTeam = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (externalId)=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.deleteTeam(externalId),\n        onSuccess: ()=>{\n            // Invalidate and refetch teams data\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n        }\n    });\n    return {\n        deleteTeam: deleteTeam.mutate,\n        isDeleteLoading: deleteTeam.isLoading,\n        deleteError: deleteTeam.error\n    };\n};\nconst useSearchTeams = function(query) {\n    let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _searchQuery_data, _searchQuery_data1;\n    const searchQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"search\",\n            query,\n            filters\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.searchTeams(query, filters),\n        enabled: !!query && query.length >= 2,\n        staleTime: 2 * 60 * 1000\n    });\n    return {\n        searchResults: ((_searchQuery_data = searchQuery.data) === null || _searchQuery_data === void 0 ? void 0 : _searchQuery_data.data) || [],\n        searchMeta: (_searchQuery_data1 = searchQuery.data) === null || _searchQuery_data1 === void 0 ? void 0 : _searchQuery_data1.meta,\n        isSearchLoading: searchQuery.isLoading,\n        searchError: searchQuery.error,\n        refetchSearch: searchQuery.refetch\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/useTeams.ts\n"));

/***/ })

});
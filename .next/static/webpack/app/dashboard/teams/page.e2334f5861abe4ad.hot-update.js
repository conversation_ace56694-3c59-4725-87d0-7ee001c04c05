"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/teams/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/useTeams.ts":
/*!***********************************!*\
  !*** ./src/lib/hooks/useTeams.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSearchTeams: function() { return /* binding */ useSearchTeams; },\n/* harmony export */   useTeam: function() { return /* binding */ useTeam; },\n/* harmony export */   useTeamMutations: function() { return /* binding */ useTeamMutations; },\n/* harmony export */   useTeamStatistics: function() { return /* binding */ useTeamStatistics; },\n/* harmony export */   useTeams: function() { return /* binding */ useTeams; },\n/* harmony export */   useTeamsByCountry: function() { return /* binding */ useTeamsByCountry; },\n/* harmony export */   useTeamsByLeague: function() { return /* binding */ useTeamsByLeague; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _lib_api_teams__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/teams */ \"(app-pages-browser)/./src/lib/api/teams.ts\");\n\n\nconst useTeams = function() {\n    let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _teamsQuery_data, _teamsQuery_data1;\n    const teamsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            filters\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeams(filters),\n        staleTime: 10 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsQuery_data = teamsQuery.data) === null || _teamsQuery_data === void 0 ? void 0 : _teamsQuery_data.data) || [],\n        teamsMeta: (_teamsQuery_data1 = teamsQuery.data) === null || _teamsQuery_data1 === void 0 ? void 0 : _teamsQuery_data1.meta,\n        isLoading: teamsQuery.isLoading,\n        error: teamsQuery.error,\n        refetch: teamsQuery.refetch\n    };\n};\nconst useTeam = (externalId)=>{\n    const teamQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"team\",\n            \"detail\",\n            externalId\n        ],\n        queryFn: async ()=>{\n            console.log(\"\\uD83D\\uDD04 useTeam - Fetching team with ID:\", externalId);\n            try {\n                const result = await _lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamById(externalId);\n                console.log(\"✅ useTeam - Success:\", result);\n                return result;\n            } catch (error) {\n                console.error(\"❌ useTeam - Error:\", error);\n                throw error;\n            }\n        },\n        enabled: !!externalId && externalId > 0,\n        staleTime: 5 * 60 * 1000,\n        retry: (failureCount, error)=>{\n            console.log(\"\\uD83D\\uDD04 useTeam - Retry attempt:\", failureCount, error);\n            return failureCount < 3;\n        },\n        retryDelay: 1000\n    });\n    console.log(\"\\uD83D\\uDD0D useTeam - Query state:\", {\n        externalId,\n        data: teamQuery.data,\n        isLoading: teamQuery.isLoading,\n        error: teamQuery.error,\n        status: teamQuery.status,\n        fetchStatus: teamQuery.fetchStatus\n    });\n    return {\n        team: teamQuery.data,\n        isLoading: teamQuery.isLoading,\n        error: teamQuery.error,\n        refetch: teamQuery.refetch\n    };\n};\nconst useTeamsByLeague = (league, season)=>{\n    var _teamsByLeagueQuery_data, _teamsByLeagueQuery_data1;\n    const teamsByLeagueQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"by-league\",\n            league,\n            season\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamsByLeague(league, season),\n        enabled: !!league,\n        staleTime: 15 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsByLeagueQuery_data = teamsByLeagueQuery.data) === null || _teamsByLeagueQuery_data === void 0 ? void 0 : _teamsByLeagueQuery_data.data) || [],\n        teamsMeta: (_teamsByLeagueQuery_data1 = teamsByLeagueQuery.data) === null || _teamsByLeagueQuery_data1 === void 0 ? void 0 : _teamsByLeagueQuery_data1.meta,\n        isLoading: teamsByLeagueQuery.isLoading,\n        error: teamsByLeagueQuery.error,\n        refetch: teamsByLeagueQuery.refetch\n    };\n};\nconst useTeamsByCountry = (country)=>{\n    var _teamsByCountryQuery_data, _teamsByCountryQuery_data1;\n    const teamsByCountryQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"by-country\",\n            country\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.getTeamsByCountry(country),\n        enabled: !!country,\n        staleTime: 15 * 60 * 1000\n    });\n    return {\n        teams: ((_teamsByCountryQuery_data = teamsByCountryQuery.data) === null || _teamsByCountryQuery_data === void 0 ? void 0 : _teamsByCountryQuery_data.data) || [],\n        teamsMeta: (_teamsByCountryQuery_data1 = teamsByCountryQuery.data) === null || _teamsByCountryQuery_data1 === void 0 ? void 0 : _teamsByCountryQuery_data1.meta,\n        isLoading: teamsByCountryQuery.isLoading,\n        error: teamsByCountryQuery.error,\n        refetch: teamsByCountryQuery.refetch\n    };\n};\nconst useTeamStatistics = (teamId)=>{\n    const statisticsQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"statistics\",\n            teamId\n        ],\n        queryFn: ()=>{\n            // For now, return mock data since we need league and season info\n            // In real implementation, this would call teamsApi.getTeamStatistics with proper params\n            return Promise.resolve({\n                totalMatches: 28,\n                wins: 18,\n                draws: 6,\n                losses: 4,\n                goalsScored: 54,\n                goalsConceded: 23,\n                cleanSheets: 12,\n                winPercentage: 64.3,\n                avgGoalsPerMatch: 1.93,\n                avgGoalsConcededPerMatch: 0.82,\n                homeRecord: {\n                    wins: 11,\n                    draws: 3,\n                    losses: 0\n                },\n                awayRecord: {\n                    wins: 7,\n                    draws: 3,\n                    losses: 4\n                },\n                recentForm: [\n                    \"W\",\n                    \"W\",\n                    \"D\",\n                    \"W\",\n                    \"L\"\n                ]\n            });\n        },\n        enabled: !!teamId,\n        staleTime: 5 * 60 * 1000\n    });\n    return {\n        statistics: statisticsQuery.data,\n        isLoading: statisticsQuery.isLoading,\n        error: statisticsQuery.error,\n        refetch: statisticsQuery.refetch\n    };\n};\nconst useTeamMutations = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    const deleteTeam = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (externalId)=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.deleteTeam(externalId),\n        onSuccess: ()=>{\n            // Invalidate and refetch teams data\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"teams\"\n                ]\n            });\n        }\n    });\n    return {\n        deleteTeam: deleteTeam.mutate,\n        isDeleteLoading: deleteTeam.isLoading,\n        deleteError: deleteTeam.error\n    };\n};\nconst useSearchTeams = function(query) {\n    let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _searchQuery_data, _searchQuery_data1;\n    const searchQuery = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"teams\",\n            \"search\",\n            query,\n            filters\n        ],\n        queryFn: ()=>_lib_api_teams__WEBPACK_IMPORTED_MODULE_0__.teamsApi.searchTeams(query, filters),\n        enabled: !!query && query.length >= 2,\n        staleTime: 2 * 60 * 1000\n    });\n    return {\n        searchResults: ((_searchQuery_data = searchQuery.data) === null || _searchQuery_data === void 0 ? void 0 : _searchQuery_data.data) || [],\n        searchMeta: (_searchQuery_data1 = searchQuery.data) === null || _searchQuery_data1 === void 0 ? void 0 : _searchQuery_data1.meta,\n        isSearchLoading: searchQuery.isLoading,\n        searchError: searchQuery.error,\n        refetchSearch: searchQuery.refetch\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvaG9va3MvdXNlVGVhbXMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBOEU7QUFDdEI7QUFFakQsTUFBTUksV0FBVztRQUFDQywyRUFBdUIsQ0FBQztRQVE5QkMsa0JBQ0lBO0lBUmpCLE1BQU1BLGFBQWFOLCtEQUFRQSxDQUFDO1FBQ3RCTyxVQUFVO1lBQUM7WUFBU0Y7U0FBUTtRQUM1QkcsU0FBUyxJQUFNTCxvREFBUUEsQ0FBQ00sUUFBUSxDQUFDSjtRQUNqQ0ssV0FBVyxLQUFLLEtBQUs7SUFDM0I7SUFFQSxPQUFPO1FBQ0RDLE9BQU9MLEVBQUFBLG1CQUFBQSxXQUFXTSxJQUFJLGNBQWZOLHVDQUFBQSxpQkFBaUJNLElBQUksS0FBSSxFQUFFO1FBQ2xDQyxTQUFTLEdBQUVQLG9CQUFBQSxXQUFXTSxJQUFJLGNBQWZOLHdDQUFBQSxrQkFBaUJRLElBQUk7UUFDaENDLFdBQVdULFdBQVdTLFNBQVM7UUFDL0JDLE9BQU9WLFdBQVdVLEtBQUs7UUFDdkJDLFNBQVNYLFdBQVdXLE9BQU87SUFDakM7QUFDTixFQUFFO0FBRUssTUFBTUMsVUFBVSxDQUFDQztJQUNsQixNQUFNQyxZQUFZcEIsK0RBQVFBLENBQUM7UUFDckJPLFVBQVU7WUFBQztZQUFRO1lBQVVZO1NBQVc7UUFDeENYLFNBQVM7WUFDSGEsUUFBUUMsR0FBRyxDQUFDLGlEQUF1Q0g7WUFDbkQsSUFBSTtnQkFDRSxNQUFNSSxTQUFTLE1BQU1wQixvREFBUUEsQ0FBQ3FCLFdBQVcsQ0FBQ0w7Z0JBQzFDRSxRQUFRQyxHQUFHLENBQUMsd0JBQXdCQztnQkFDcEMsT0FBT0E7WUFDYixFQUFFLE9BQU9QLE9BQU87Z0JBQ1ZLLFFBQVFMLEtBQUssQ0FBQyxzQkFBc0JBO2dCQUNwQyxNQUFNQTtZQUNaO1FBQ047UUFDQVMsU0FBUyxDQUFDLENBQUNOLGNBQWNBLGFBQWE7UUFDdENULFdBQVcsSUFBSSxLQUFLO1FBQ3BCZ0IsT0FBTyxDQUFDQyxjQUFjWDtZQUNoQkssUUFBUUMsR0FBRyxDQUFDLHlDQUErQkssY0FBY1g7WUFDekQsT0FBT1csZUFBZTtRQUM1QjtRQUNBQyxZQUFZO0lBQ2xCO0lBRUFQLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBNkI7UUFDbkNIO1FBQ0FQLE1BQU1RLFVBQVVSLElBQUk7UUFDcEJHLFdBQVdLLFVBQVVMLFNBQVM7UUFDOUJDLE9BQU9JLFVBQVVKLEtBQUs7UUFDdEJhLFFBQVFULFVBQVVTLE1BQU07UUFDeEJDLGFBQWFWLFVBQVVVLFdBQVc7SUFDeEM7SUFFQSxPQUFPO1FBQ0RDLE1BQU1YLFVBQVVSLElBQUk7UUFDcEJHLFdBQVdLLFVBQVVMLFNBQVM7UUFDOUJDLE9BQU9JLFVBQVVKLEtBQUs7UUFDdEJDLFNBQVNHLFVBQVVILE9BQU87SUFDaEM7QUFDTixFQUFFO0FBRUssTUFBTWUsbUJBQW1CLENBQUNDLFFBQWdCQztRQVM5QkMsMEJBQ0lBO0lBVGpCLE1BQU1BLHFCQUFxQm5DLCtEQUFRQSxDQUFDO1FBQzlCTyxVQUFVO1lBQUM7WUFBUztZQUFhMEI7WUFBUUM7U0FBTztRQUNoRDFCLFNBQVMsSUFBTUwsb0RBQVFBLENBQUNpQyxnQkFBZ0IsQ0FBQ0gsUUFBUUM7UUFDakRULFNBQVMsQ0FBQyxDQUFDUTtRQUNYdkIsV0FBVyxLQUFLLEtBQUs7SUFDM0I7SUFFQSxPQUFPO1FBQ0RDLE9BQU93QixFQUFBQSwyQkFBQUEsbUJBQW1CdkIsSUFBSSxjQUF2QnVCLCtDQUFBQSx5QkFBeUJ2QixJQUFJLEtBQUksRUFBRTtRQUMxQ0MsU0FBUyxHQUFFc0IsNEJBQUFBLG1CQUFtQnZCLElBQUksY0FBdkJ1QixnREFBQUEsMEJBQXlCckIsSUFBSTtRQUN4Q0MsV0FBV29CLG1CQUFtQnBCLFNBQVM7UUFDdkNDLE9BQU9tQixtQkFBbUJuQixLQUFLO1FBQy9CQyxTQUFTa0IsbUJBQW1CbEIsT0FBTztJQUN6QztBQUNOLEVBQUU7QUFFSyxNQUFNb0Isb0JBQW9CLENBQUNDO1FBU2ZDLDJCQUNJQTtJQVRqQixNQUFNQSxzQkFBc0J2QywrREFBUUEsQ0FBQztRQUMvQk8sVUFBVTtZQUFDO1lBQVM7WUFBYytCO1NBQVE7UUFDMUM5QixTQUFTLElBQU1MLG9EQUFRQSxDQUFDcUMsaUJBQWlCLENBQUNGO1FBQzFDYixTQUFTLENBQUMsQ0FBQ2E7UUFDWDVCLFdBQVcsS0FBSyxLQUFLO0lBQzNCO0lBRUEsT0FBTztRQUNEQyxPQUFPNEIsRUFBQUEsNEJBQUFBLG9CQUFvQjNCLElBQUksY0FBeEIyQixnREFBQUEsMEJBQTBCM0IsSUFBSSxLQUFJLEVBQUU7UUFDM0NDLFNBQVMsR0FBRTBCLDZCQUFBQSxvQkFBb0IzQixJQUFJLGNBQXhCMkIsaURBQUFBLDJCQUEwQnpCLElBQUk7UUFDekNDLFdBQVd3QixvQkFBb0J4QixTQUFTO1FBQ3hDQyxPQUFPdUIsb0JBQW9CdkIsS0FBSztRQUNoQ0MsU0FBU3NCLG9CQUFvQnRCLE9BQU87SUFDMUM7QUFDTixFQUFFO0FBRUssTUFBTXdCLG9CQUFvQixDQUFDQztJQUM1QixNQUFNQyxrQkFBa0IzQywrREFBUUEsQ0FBQztRQUMzQk8sVUFBVTtZQUFDO1lBQVM7WUFBY21DO1NBQU87UUFDekNsQyxTQUFTO1lBQ0gsaUVBQWlFO1lBQ2pFLHdGQUF3RjtZQUN4RixPQUFPb0MsUUFBUUMsT0FBTyxDQUFDO2dCQUNqQkMsY0FBYztnQkFDZEMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsZUFBZTtnQkFDZkMsYUFBYTtnQkFDYkMsZUFBZTtnQkFDZkMsa0JBQWtCO2dCQUNsQkMsMEJBQTBCO2dCQUMxQkMsWUFBWTtvQkFBRVQsTUFBTTtvQkFBSUMsT0FBTztvQkFBR0MsUUFBUTtnQkFBRTtnQkFDNUNRLFlBQVk7b0JBQUVWLE1BQU07b0JBQUdDLE9BQU87b0JBQUdDLFFBQVE7Z0JBQUU7Z0JBQzNDUyxZQUFZO29CQUFDO29CQUFLO29CQUFLO29CQUFLO29CQUFLO2lCQUFJO1lBQzNDO1FBQ047UUFDQWpDLFNBQVMsQ0FBQyxDQUFDaUI7UUFDWGhDLFdBQVcsSUFBSSxLQUFLO0lBQzFCO0lBRUEsT0FBTztRQUNEaUQsWUFBWWhCLGdCQUFnQi9CLElBQUk7UUFDaENHLFdBQVc0QixnQkFBZ0I1QixTQUFTO1FBQ3BDQyxPQUFPMkIsZ0JBQWdCM0IsS0FBSztRQUM1QkMsU0FBUzBCLGdCQUFnQjFCLE9BQU87SUFDdEM7QUFDTixFQUFFO0FBRUssTUFBTTJDLG1CQUFtQjtJQUMxQixNQUFNQyxjQUFjM0QscUVBQWNBO0lBRWxDLE1BQU00RCxhQUFhN0Qsa0VBQVdBLENBQUM7UUFDekI4RCxZQUFZLENBQUM1QyxhQUF1QmhCLG9EQUFRQSxDQUFDMkQsVUFBVSxDQUFDM0M7UUFDeEQ2QyxXQUFXO1lBQ0wsb0NBQW9DO1lBQ3BDSCxZQUFZSSxpQkFBaUIsQ0FBQztnQkFBRTFELFVBQVU7b0JBQUM7aUJBQVE7WUFBQztRQUMxRDtJQUNOO0lBRUEsT0FBTztRQUNEdUQsWUFBWUEsV0FBV0ksTUFBTTtRQUM3QkMsaUJBQWlCTCxXQUFXL0MsU0FBUztRQUNyQ3FELGFBQWFOLFdBQVc5QyxLQUFLO0lBQ25DO0FBQ04sRUFBRTtBQUVLLE1BQU1xRCxpQkFBaUIsU0FBQ0M7UUFBZWpFLDJFQUF1QixDQUFDO1FBUzNDa0UsbUJBQ0hBO0lBVGxCLE1BQU1BLGNBQWN2RSwrREFBUUEsQ0FBQztRQUN2Qk8sVUFBVTtZQUFDO1lBQVM7WUFBVStEO1lBQU9qRTtTQUFRO1FBQzdDRyxTQUFTLElBQU1MLG9EQUFRQSxDQUFDcUUsV0FBVyxDQUFDRixPQUFPakU7UUFDM0NvQixTQUFTLENBQUMsQ0FBQzZDLFNBQVNBLE1BQU1HLE1BQU0sSUFBSTtRQUNwQy9ELFdBQVcsSUFBSSxLQUFLO0lBQzFCO0lBRUEsT0FBTztRQUNEZ0UsZUFBZUgsRUFBQUEsb0JBQUFBLFlBQVkzRCxJQUFJLGNBQWhCMkQsd0NBQUFBLGtCQUFrQjNELElBQUksS0FBSSxFQUFFO1FBQzNDK0QsVUFBVSxHQUFFSixxQkFBQUEsWUFBWTNELElBQUksY0FBaEIyRCx5Q0FBQUEsbUJBQWtCekQsSUFBSTtRQUNsQzhELGlCQUFpQkwsWUFBWXhELFNBQVM7UUFDdEM4RCxhQUFhTixZQUFZdkQsS0FBSztRQUM5QjhELGVBQWVQLFlBQVl0RCxPQUFPO0lBQ3hDO0FBQ04sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvbGliL2hvb2tzL3VzZVRlYW1zLnRzP2VlNjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlUXVlcnksIHVzZU11dGF0aW9uLCB1c2VRdWVyeUNsaWVudCB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG5pbXBvcnQgeyB0ZWFtc0FwaSwgVGVhbUZpbHRlcnMgfSBmcm9tICdAL2xpYi9hcGkvdGVhbXMnO1xuXG5leHBvcnQgY29uc3QgdXNlVGVhbXMgPSAoZmlsdGVyczogVGVhbUZpbHRlcnMgPSB7fSkgPT4ge1xuICAgICAgY29uc3QgdGVhbXNRdWVyeSA9IHVzZVF1ZXJ5KHtcbiAgICAgICAgICAgIHF1ZXJ5S2V5OiBbJ3RlYW1zJywgZmlsdGVyc10sXG4gICAgICAgICAgICBxdWVyeUZuOiAoKSA9PiB0ZWFtc0FwaS5nZXRUZWFtcyhmaWx0ZXJzKSxcbiAgICAgICAgICAgIHN0YWxlVGltZTogMTAgKiA2MCAqIDEwMDAsIC8vIDEwIG1pbnV0ZXMgLSB0ZWFtcyBkb24ndCBjaGFuZ2Ugb2Z0ZW5cbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdGVhbXM6IHRlYW1zUXVlcnkuZGF0YT8uZGF0YSB8fCBbXSxcbiAgICAgICAgICAgIHRlYW1zTWV0YTogdGVhbXNRdWVyeS5kYXRhPy5tZXRhLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiB0ZWFtc1F1ZXJ5LmlzTG9hZGluZyxcbiAgICAgICAgICAgIGVycm9yOiB0ZWFtc1F1ZXJ5LmVycm9yLFxuICAgICAgICAgICAgcmVmZXRjaDogdGVhbXNRdWVyeS5yZWZldGNoLFxuICAgICAgfTtcbn07XG5cbmV4cG9ydCBjb25zdCB1c2VUZWFtID0gKGV4dGVybmFsSWQ6IG51bWJlcikgPT4ge1xuICAgICAgY29uc3QgdGVhbVF1ZXJ5ID0gdXNlUXVlcnkoe1xuICAgICAgICAgICAgcXVlcnlLZXk6IFsndGVhbScsICdkZXRhaWwnLCBleHRlcm5hbElkXSxcbiAgICAgICAgICAgIHF1ZXJ5Rm46IGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIHVzZVRlYW0gLSBGZXRjaGluZyB0ZWFtIHdpdGggSUQ6JywgZXh0ZXJuYWxJZCk7XG4gICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgdGVhbXNBcGkuZ2V0VGVhbUJ5SWQoZXh0ZXJuYWxJZCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIHVzZVRlYW0gLSBTdWNjZXNzOicsIHJlc3VsdCk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCB1c2VUZWFtIC0gRXJyb3I6JywgZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZW5hYmxlZDogISFleHRlcm5hbElkICYmIGV4dGVybmFsSWQgPiAwLFxuICAgICAgICAgICAgc3RhbGVUaW1lOiA1ICogNjAgKiAxMDAwLCAvLyA1IG1pbnV0ZXNcbiAgICAgICAgICAgIHJldHJ5OiAoZmFpbHVyZUNvdW50LCBlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflIQgdXNlVGVhbSAtIFJldHJ5IGF0dGVtcHQ6JywgZmFpbHVyZUNvdW50LCBlcnJvcik7XG4gICAgICAgICAgICAgICAgICByZXR1cm4gZmFpbHVyZUNvdW50IDwgMztcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICByZXRyeURlbGF5OiAxMDAwLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIHVzZVRlYW0gLSBRdWVyeSBzdGF0ZTonLCB7XG4gICAgICAgICAgICBleHRlcm5hbElkLFxuICAgICAgICAgICAgZGF0YTogdGVhbVF1ZXJ5LmRhdGEsXG4gICAgICAgICAgICBpc0xvYWRpbmc6IHRlYW1RdWVyeS5pc0xvYWRpbmcsXG4gICAgICAgICAgICBlcnJvcjogdGVhbVF1ZXJ5LmVycm9yLFxuICAgICAgICAgICAgc3RhdHVzOiB0ZWFtUXVlcnkuc3RhdHVzLFxuICAgICAgICAgICAgZmV0Y2hTdGF0dXM6IHRlYW1RdWVyeS5mZXRjaFN0YXR1c1xuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0ZWFtOiB0ZWFtUXVlcnkuZGF0YSxcbiAgICAgICAgICAgIGlzTG9hZGluZzogdGVhbVF1ZXJ5LmlzTG9hZGluZyxcbiAgICAgICAgICAgIGVycm9yOiB0ZWFtUXVlcnkuZXJyb3IsXG4gICAgICAgICAgICByZWZldGNoOiB0ZWFtUXVlcnkucmVmZXRjaCxcbiAgICAgIH07XG59O1xuXG5leHBvcnQgY29uc3QgdXNlVGVhbXNCeUxlYWd1ZSA9IChsZWFndWU6IG51bWJlciwgc2Vhc29uPzogbnVtYmVyKSA9PiB7XG4gICAgICBjb25zdCB0ZWFtc0J5TGVhZ3VlUXVlcnkgPSB1c2VRdWVyeSh7XG4gICAgICAgICAgICBxdWVyeUtleTogWyd0ZWFtcycsICdieS1sZWFndWUnLCBsZWFndWUsIHNlYXNvbl0sXG4gICAgICAgICAgICBxdWVyeUZuOiAoKSA9PiB0ZWFtc0FwaS5nZXRUZWFtc0J5TGVhZ3VlKGxlYWd1ZSwgc2Vhc29uKSxcbiAgICAgICAgICAgIGVuYWJsZWQ6ICEhbGVhZ3VlLFxuICAgICAgICAgICAgc3RhbGVUaW1lOiAxNSAqIDYwICogMTAwMCwgLy8gMTUgbWludXRlc1xuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0ZWFtczogdGVhbXNCeUxlYWd1ZVF1ZXJ5LmRhdGE/LmRhdGEgfHwgW10sXG4gICAgICAgICAgICB0ZWFtc01ldGE6IHRlYW1zQnlMZWFndWVRdWVyeS5kYXRhPy5tZXRhLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiB0ZWFtc0J5TGVhZ3VlUXVlcnkuaXNMb2FkaW5nLFxuICAgICAgICAgICAgZXJyb3I6IHRlYW1zQnlMZWFndWVRdWVyeS5lcnJvcixcbiAgICAgICAgICAgIHJlZmV0Y2g6IHRlYW1zQnlMZWFndWVRdWVyeS5yZWZldGNoLFxuICAgICAgfTtcbn07XG5cbmV4cG9ydCBjb25zdCB1c2VUZWFtc0J5Q291bnRyeSA9IChjb3VudHJ5OiBzdHJpbmcpID0+IHtcbiAgICAgIGNvbnN0IHRlYW1zQnlDb3VudHJ5UXVlcnkgPSB1c2VRdWVyeSh7XG4gICAgICAgICAgICBxdWVyeUtleTogWyd0ZWFtcycsICdieS1jb3VudHJ5JywgY291bnRyeV0sXG4gICAgICAgICAgICBxdWVyeUZuOiAoKSA9PiB0ZWFtc0FwaS5nZXRUZWFtc0J5Q291bnRyeShjb3VudHJ5KSxcbiAgICAgICAgICAgIGVuYWJsZWQ6ICEhY291bnRyeSxcbiAgICAgICAgICAgIHN0YWxlVGltZTogMTUgKiA2MCAqIDEwMDAsIC8vIDE1IG1pbnV0ZXNcbiAgICAgIH0pO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdGVhbXM6IHRlYW1zQnlDb3VudHJ5UXVlcnkuZGF0YT8uZGF0YSB8fCBbXSxcbiAgICAgICAgICAgIHRlYW1zTWV0YTogdGVhbXNCeUNvdW50cnlRdWVyeS5kYXRhPy5tZXRhLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiB0ZWFtc0J5Q291bnRyeVF1ZXJ5LmlzTG9hZGluZyxcbiAgICAgICAgICAgIGVycm9yOiB0ZWFtc0J5Q291bnRyeVF1ZXJ5LmVycm9yLFxuICAgICAgICAgICAgcmVmZXRjaDogdGVhbXNCeUNvdW50cnlRdWVyeS5yZWZldGNoLFxuICAgICAgfTtcbn07XG5cbmV4cG9ydCBjb25zdCB1c2VUZWFtU3RhdGlzdGljcyA9ICh0ZWFtSWQ6IG51bWJlcikgPT4ge1xuICAgICAgY29uc3Qgc3RhdGlzdGljc1F1ZXJ5ID0gdXNlUXVlcnkoe1xuICAgICAgICAgICAgcXVlcnlLZXk6IFsndGVhbXMnLCAnc3RhdGlzdGljcycsIHRlYW1JZF0sXG4gICAgICAgICAgICBxdWVyeUZuOiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAvLyBGb3Igbm93LCByZXR1cm4gbW9jayBkYXRhIHNpbmNlIHdlIG5lZWQgbGVhZ3VlIGFuZCBzZWFzb24gaW5mb1xuICAgICAgICAgICAgICAgICAgLy8gSW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBjYWxsIHRlYW1zQXBpLmdldFRlYW1TdGF0aXN0aWNzIHdpdGggcHJvcGVyIHBhcmFtc1xuICAgICAgICAgICAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICB0b3RhbE1hdGNoZXM6IDI4LFxuICAgICAgICAgICAgICAgICAgICAgICAgd2luczogMTgsXG4gICAgICAgICAgICAgICAgICAgICAgICBkcmF3czogNixcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvc3NlczogNCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGdvYWxzU2NvcmVkOiA1NCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGdvYWxzQ29uY2VkZWQ6IDIzLFxuICAgICAgICAgICAgICAgICAgICAgICAgY2xlYW5TaGVldHM6IDEyLFxuICAgICAgICAgICAgICAgICAgICAgICAgd2luUGVyY2VudGFnZTogNjQuMyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGF2Z0dvYWxzUGVyTWF0Y2g6IDEuOTMsXG4gICAgICAgICAgICAgICAgICAgICAgICBhdmdHb2Fsc0NvbmNlZGVkUGVyTWF0Y2g6IDAuODIsXG4gICAgICAgICAgICAgICAgICAgICAgICBob21lUmVjb3JkOiB7IHdpbnM6IDExLCBkcmF3czogMywgbG9zc2VzOiAwIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICBhd2F5UmVjb3JkOiB7IHdpbnM6IDcsIGRyYXdzOiAzLCBsb3NzZXM6IDQgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlY2VudEZvcm06IFsnVycsICdXJywgJ0QnLCAnVycsICdMJ10sXG4gICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBlbmFibGVkOiAhIXRlYW1JZCxcbiAgICAgICAgICAgIHN0YWxlVGltZTogNSAqIDYwICogMTAwMCwgLy8gNSBtaW51dGVzXG4gICAgICB9KTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHN0YXRpc3RpY3M6IHN0YXRpc3RpY3NRdWVyeS5kYXRhLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiBzdGF0aXN0aWNzUXVlcnkuaXNMb2FkaW5nLFxuICAgICAgICAgICAgZXJyb3I6IHN0YXRpc3RpY3NRdWVyeS5lcnJvcixcbiAgICAgICAgICAgIHJlZmV0Y2g6IHN0YXRpc3RpY3NRdWVyeS5yZWZldGNoLFxuICAgICAgfTtcbn07XG5cbmV4cG9ydCBjb25zdCB1c2VUZWFtTXV0YXRpb25zID0gKCkgPT4ge1xuICAgICAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpO1xuXG4gICAgICBjb25zdCBkZWxldGVUZWFtID0gdXNlTXV0YXRpb24oe1xuICAgICAgICAgICAgbXV0YXRpb25GbjogKGV4dGVybmFsSWQ6IG51bWJlcikgPT4gdGVhbXNBcGkuZGVsZXRlVGVhbShleHRlcm5hbElkKSxcbiAgICAgICAgICAgIG9uU3VjY2VzczogKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgLy8gSW52YWxpZGF0ZSBhbmQgcmVmZXRjaCB0ZWFtcyBkYXRhXG4gICAgICAgICAgICAgICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3RlYW1zJ10gfSk7XG4gICAgICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBkZWxldGVUZWFtOiBkZWxldGVUZWFtLm11dGF0ZSxcbiAgICAgICAgICAgIGlzRGVsZXRlTG9hZGluZzogZGVsZXRlVGVhbS5pc0xvYWRpbmcsXG4gICAgICAgICAgICBkZWxldGVFcnJvcjogZGVsZXRlVGVhbS5lcnJvcixcbiAgICAgIH07XG59O1xuXG5leHBvcnQgY29uc3QgdXNlU2VhcmNoVGVhbXMgPSAocXVlcnk6IHN0cmluZywgZmlsdGVyczogVGVhbUZpbHRlcnMgPSB7fSkgPT4ge1xuICAgICAgY29uc3Qgc2VhcmNoUXVlcnkgPSB1c2VRdWVyeSh7XG4gICAgICAgICAgICBxdWVyeUtleTogWyd0ZWFtcycsICdzZWFyY2gnLCBxdWVyeSwgZmlsdGVyc10sXG4gICAgICAgICAgICBxdWVyeUZuOiAoKSA9PiB0ZWFtc0FwaS5zZWFyY2hUZWFtcyhxdWVyeSwgZmlsdGVycyksXG4gICAgICAgICAgICBlbmFibGVkOiAhIXF1ZXJ5ICYmIHF1ZXJ5Lmxlbmd0aCA+PSAyLCAvLyBPbmx5IHNlYXJjaCB3aXRoIDIrIGNoYXJhY3RlcnNcbiAgICAgICAgICAgIHN0YWxlVGltZTogMiAqIDYwICogMTAwMCwgLy8gMiBtaW51dGVzIGZvciBzZWFyY2ggcmVzdWx0c1xuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBzZWFyY2hSZXN1bHRzOiBzZWFyY2hRdWVyeS5kYXRhPy5kYXRhIHx8IFtdLFxuICAgICAgICAgICAgc2VhcmNoTWV0YTogc2VhcmNoUXVlcnkuZGF0YT8ubWV0YSxcbiAgICAgICAgICAgIGlzU2VhcmNoTG9hZGluZzogc2VhcmNoUXVlcnkuaXNMb2FkaW5nLFxuICAgICAgICAgICAgc2VhcmNoRXJyb3I6IHNlYXJjaFF1ZXJ5LmVycm9yLFxuICAgICAgICAgICAgcmVmZXRjaFNlYXJjaDogc2VhcmNoUXVlcnkucmVmZXRjaCxcbiAgICAgIH07XG59O1xuIl0sIm5hbWVzIjpbInVzZVF1ZXJ5IiwidXNlTXV0YXRpb24iLCJ1c2VRdWVyeUNsaWVudCIsInRlYW1zQXBpIiwidXNlVGVhbXMiLCJmaWx0ZXJzIiwidGVhbXNRdWVyeSIsInF1ZXJ5S2V5IiwicXVlcnlGbiIsImdldFRlYW1zIiwic3RhbGVUaW1lIiwidGVhbXMiLCJkYXRhIiwidGVhbXNNZXRhIiwibWV0YSIsImlzTG9hZGluZyIsImVycm9yIiwicmVmZXRjaCIsInVzZVRlYW0iLCJleHRlcm5hbElkIiwidGVhbVF1ZXJ5IiwiY29uc29sZSIsImxvZyIsInJlc3VsdCIsImdldFRlYW1CeUlkIiwiZW5hYmxlZCIsInJldHJ5IiwiZmFpbHVyZUNvdW50IiwicmV0cnlEZWxheSIsInN0YXR1cyIsImZldGNoU3RhdHVzIiwidGVhbSIsInVzZVRlYW1zQnlMZWFndWUiLCJsZWFndWUiLCJzZWFzb24iLCJ0ZWFtc0J5TGVhZ3VlUXVlcnkiLCJnZXRUZWFtc0J5TGVhZ3VlIiwidXNlVGVhbXNCeUNvdW50cnkiLCJjb3VudHJ5IiwidGVhbXNCeUNvdW50cnlRdWVyeSIsImdldFRlYW1zQnlDb3VudHJ5IiwidXNlVGVhbVN0YXRpc3RpY3MiLCJ0ZWFtSWQiLCJzdGF0aXN0aWNzUXVlcnkiLCJQcm9taXNlIiwicmVzb2x2ZSIsInRvdGFsTWF0Y2hlcyIsIndpbnMiLCJkcmF3cyIsImxvc3NlcyIsImdvYWxzU2NvcmVkIiwiZ29hbHNDb25jZWRlZCIsImNsZWFuU2hlZXRzIiwid2luUGVyY2VudGFnZSIsImF2Z0dvYWxzUGVyTWF0Y2giLCJhdmdHb2Fsc0NvbmNlZGVkUGVyTWF0Y2giLCJob21lUmVjb3JkIiwiYXdheVJlY29yZCIsInJlY2VudEZvcm0iLCJzdGF0aXN0aWNzIiwidXNlVGVhbU11dGF0aW9ucyIsInF1ZXJ5Q2xpZW50IiwiZGVsZXRlVGVhbSIsIm11dGF0aW9uRm4iLCJvblN1Y2Nlc3MiLCJpbnZhbGlkYXRlUXVlcmllcyIsIm11dGF0ZSIsImlzRGVsZXRlTG9hZGluZyIsImRlbGV0ZUVycm9yIiwidXNlU2VhcmNoVGVhbXMiLCJxdWVyeSIsInNlYXJjaFF1ZXJ5Iiwic2VhcmNoVGVhbXMiLCJsZW5ndGgiLCJzZWFyY2hSZXN1bHRzIiwic2VhcmNoTWV0YSIsImlzU2VhcmNoTG9hZGluZyIsInNlYXJjaEVycm9yIiwicmVmZXRjaFNlYXJjaCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/useTeams.ts\n"));

/***/ })

});
# 024 - Team Sync Functionality

**Date**: 2024-12-19  
**Feature**: Team Data Sync Button with `newdb=true` Parameter  
**Status**: ✅ **COMPLETED**

## 📋 **Overview**

Added sync functionality to team details page that calls the `football/teams/{id}?newdb=true` endpoint to fetch and update the latest team information from external data sources.

## 🎯 **Objectives**

1. ✅ Add sync API function to teams service
2. ✅ Create sync mutation hook for React Query
3. ✅ Add sync button to team details page UI
4. ✅ Update API proxy to support `newdb=true` parameter
5. ✅ Enhance Team interface to support venue object
6. ✅ Improve UI to display enhanced venue information
7. ✅ Add proper loading states and error handling

## 🔧 **Implementation Details**

### **1. Teams API Service Enhancement**
**File**: `src/lib/api/teams.ts`

```typescript
// Sync team data from external source (requires authentication)
syncTeam: async (externalId: number): Promise<Team> => {
  const response = await fetch(`/api/teams/${externalId}?newdb=true`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to sync team data');
  }

  const data = await response.json();
  return data.data; // Extract team data from API response
},
```

### **2. React Query Sync Mutation**
**File**: `src/lib/hooks/useTeams.ts`

```typescript
const syncTeam = useMutation({
  mutationFn: (externalId: number) => teamsApi.syncTeam(externalId),
  onSuccess: (data, externalId) => {
    // Invalidate and refetch team data
    queryClient.invalidateQueries({ queryKey: ['team', 'detail', externalId] });
    queryClient.invalidateQueries({ queryKey: ['teams'] });
    // Update the cache with new data
    queryClient.setQueryData(['team', 'detail', externalId], data);
  },
});
```

### **3. Enhanced Team Interface**
**File**: `src/lib/types/api.ts`

```typescript
export interface TeamVenue {
  id: number;
  name: string;
  address: string;
  city: string;
  capacity: number;
  surface: string;
  image: string;
}

export interface Team {
  id: number;
  externalId: number;
  name: string;
  code?: string | null;
  country?: string | null;
  founded?: number | null;
  logo: string;
  season?: number | null;
  leagueId?: number | null;
  national?: boolean;
  venue?: string | TeamVenue | null;
}
```

### **4. API Proxy Enhancement**
**File**: `src/app/api/teams/[id]/route.ts`

```typescript
// Log if this is a sync request
if (searchParams.get('newdb') === 'true') {
  console.log('🔄 Team sync request detected - fetching latest data from external source');
}
```

## 🎨 **UI/UX Enhancements**

### **Sync Button**
- **Location**: Team details page header, next to Statistics button
- **Permissions**: Available for Editor+ roles
- **Visual States**: 
  - Normal: Blue outline with "Sync Data" text
  - Loading: Spinning icon with "Syncing..." text
  - Disabled during sync operation

### **Enhanced Venue Display**
- **Simple Venue**: Shows as text when venue is string
- **Detailed Venue**: Shows dedicated card with:
  - Stadium name
  - Location (city + address)
  - Capacity (formatted with commas)
  - Surface type (capitalized)

### **Sync Handler**
```typescript
const handleSync = () => {
  syncTeam(teamId, {
    onSuccess: () => {
      toast.success('Team data synced successfully');
      refetch(); // Refresh the team data
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to sync team data');
    },
  });
};
```

## 📊 **API Response Comparison**

### **Before Sync** (Regular API call):
```json
{
  "data": {
    "id": 1,
    "externalId": 127,
    "name": "Flamengo",
    "code": null,
    "country": null,
    "logo": "public/images/teams/127.png",
    "season": null,
    "leagueId": null,
    "founded": null,
    "national": false,
    "venue": null
  }
}
```

### **After Sync** (With `newdb=true`):
```json
{
  "data": {
    "id": 1,
    "externalId": 127,
    "name": "Flamengo",
    "code": "FLA",
    "country": "brazil",
    "logo": "public/images/teams/127.png",
    "season": 0,
    "leagueId": null,
    "founded": 1895,
    "national": false,
    "venue": {
      "id": 204,
      "name": "Estadio Jornalista Mário Filho (Maracanã)",
      "address": "Rua Professor Eurico Rabelo, Maracanã",
      "city": "Rio de Janeiro, Rio de Janeiro",
      "capacity": 78838,
      "surface": "grass",
      "image": "public/images/venues/127.svg"
    }
  }
}
```

## 🔍 **Key Features**

### **Sync Functionality**
1. **One-Click Sync**: Single button to fetch latest data
2. **Real-time Updates**: UI updates immediately after sync
3. **Cache Management**: React Query cache updated with new data
4. **Error Handling**: Toast notifications for success/failure
5. **Loading States**: Visual feedback during sync operation

### **Enhanced Data Display**
1. **Team Code**: Now displays when available (e.g., "FLA")
2. **Country**: Shows country name when synced
3. **Founded Year**: Displays founding year when available
4. **Venue Details**: Rich venue information including:
   - Stadium name and location
   - Capacity and surface type
   - Address information

### **Permission Control**
- **Sync Button**: Only visible to Editor+ roles
- **Authentication**: Requires valid JWT token
- **Error Handling**: Proper error messages for unauthorized access

## 🧪 **Testing**

1. **Visit team page**: `http://localhost:4000/dashboard/teams/127`
2. **Before sync**: Limited information displayed
3. **Click sync**: Blue "Sync Data" button in header
4. **After sync**: Enhanced information with venue details
5. **Verify API calls**: Check network tab for `?newdb=true` parameter

## 📁 **Files Modified**

1. `src/lib/api/teams.ts` - Added syncTeam function
2. `src/lib/hooks/useTeams.ts` - Added sync mutation
3. `src/lib/types/api.ts` - Enhanced Team interface with TeamVenue
4. `src/app/api/teams/[id]/route.ts` - Added sync request logging
5. `src/app/dashboard/teams/[id]/page.tsx` - Added sync UI and venue display

## ✅ **Completion Status**

- [x] Sync API function implemented
- [x] React Query mutation with cache management
- [x] Sync button with loading states
- [x] Enhanced Team interface for venue data
- [x] Improved venue information display
- [x] Permission-based access control
- [x] Error handling and user feedback
- [x] API proxy support for newdb parameter

## 🔄 **Usage Instructions**

1. **Navigate** to any team details page (e.g., `/dashboard/teams/127`)
2. **Login** with Editor+ role (admin/admin123456)
3. **Click** the blue "Sync Data" button in the header
4. **Wait** for sync completion (button shows spinning icon)
5. **View** enhanced team information with updated data
6. **Check** venue section for detailed stadium information

---

**Note**: The sync functionality fetches the latest team data from external sources and updates the local database, providing more comprehensive team information including venue details, team codes, and founding years.

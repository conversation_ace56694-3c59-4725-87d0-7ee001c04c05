import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const teamId = params.id;
    const { searchParams } = new URL(request.url);

    // Forward all query parameters
    const queryParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      queryParams.append(key, value);
    });

    const queryString = queryParams.toString();
    const url = `${API_BASE_URL}/football/teams/${teamId}${queryString ? `?${queryString}` : ''}`;

    console.log('🔄 Proxying team detail request:', url);

    // Log if this is a sync request
    if (searchParams.get('newdb') === 'true') {
      console.log('🔄 Team sync request detected - fetching latest data from external source');
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ Team Detail API Error:', response.status, response.statusText);
      return NextResponse.json(
        {
          error: 'Failed to fetch team details',
          status: response.status,
          message: response.statusText
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Team detail fetched successfully:', data.data?.name || 'Unknown team');

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Team Detail Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message || 'Unknown error'
      },
      { status: 500 }
    );
  }
}

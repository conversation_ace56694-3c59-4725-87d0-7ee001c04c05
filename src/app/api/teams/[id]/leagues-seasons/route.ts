import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const teamId = params.id;
    const { searchParams } = new URL(request.url);
    
    // Forward all query parameters
    const queryParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      queryParams.append(key, value);
    });

    // Default format to by-league if not specified
    if (!queryParams.has('format')) {
      queryParams.append('format', 'by-league');
    }

    const queryString = queryParams.toString();
    const url = `${API_BASE_URL}/football/teams/${teamId}/leagues-seasons${queryString ? `?${queryString}` : ''}`;

    console.log('🔄 Proxying team leagues-seasons request:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ Team Leagues-Seasons API Error:', response.status, response.statusText);
      return NextResponse.json(
        { 
          error: 'Failed to fetch team leagues and seasons',
          status: response.status,
          message: response.statusText 
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Team leagues-seasons fetched successfully:', data.data?.totalLeagues || 0, 'leagues');

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Team Leagues-Seasons Proxy Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message || 'Unknown error'
      },
      { status: 500 }
    );
  }
}

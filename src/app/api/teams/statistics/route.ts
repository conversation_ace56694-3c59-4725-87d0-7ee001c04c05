import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Required parameters
    const league = searchParams.get('league');
    const season = searchParams.get('season');
    const team = searchParams.get('team');

    if (!league || !season || !team) {
      return NextResponse.json(
        { 
          error: 'Missing required parameters',
          message: 'league, season, and team parameters are required'
        },
        { status: 400 }
      );
    }

    // Forward all query parameters
    const queryParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      queryParams.append(key, value);
    });

    const queryString = queryParams.toString();
    const url = `${API_BASE_URL}/football/teams/statistics?${queryString}`;

    console.log('🔄 Proxying team statistics request:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ Team Statistics API Error:', response.status, response.statusText);
      
      // Try to get error details
      let errorMessage = response.statusText;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch (e) {
        // If response is not JSON, use status text
      }

      return NextResponse.json(
        { 
          error: 'Failed to fetch team statistics',
          status: response.status,
          message: errorMessage
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Team statistics fetched successfully for team:', team, 'league:', league, 'season:', season);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Team Statistics Proxy Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message || 'Unknown error'
      },
      { status: 500 }
    );
  }
}

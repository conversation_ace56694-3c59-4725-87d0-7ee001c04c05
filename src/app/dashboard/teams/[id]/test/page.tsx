'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Users, Hash, Flag, Calendar, Shield, Home, Info, Trophy } from 'lucide-react';
import { buildTeamLogoUrl, buildCountryFlagUrl } from '@/lib/utils/image';

interface Team {
  id: number;
  externalId: number;
  name: string;
  code?: string | null;
  country?: string | null;
  founded?: number | null;
  logo: string;
  season?: number | null;
  leagueId?: number | null;
  national?: boolean;
  venue?: string | null;
}

export default function TeamTestPage() {
  const params = useParams();
  const router = useRouter();
  const teamId = parseInt(params.id as string);
  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTeam = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔄 Fetching team with ID:', teamId);
      
      // Get auth token
      const authStorage = localStorage.getItem('auth-storage');
      let token = null;
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        token = parsed.state?.accessToken;
      }
      
      if (!token) {
        token = localStorage.getItem('accessToken');
      }

      console.log('🔑 Using token:', token ? `${token.substring(0, 20)}...` : 'No token');

      const response = await fetch(`/api/teams/${teamId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        },
      });

      console.log('📡 Response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Response error:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Team data received:', data);
      setTeam(data.data);
    } catch (err) {
      console.error('❌ Fetch error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (teamId) {
      fetchTeam();
    }
  }, [teamId]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <p>Loading team {teamId}...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </div>
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-600">Error Loading Team {teamId}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchTeam}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!team) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Team Not Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p>No team data found for ID: {teamId}</p>
            <Button onClick={fetchTeam} className="mt-4">Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>

          <div className="flex items-center space-x-3">
            {buildTeamLogoUrl(team.logo) ? (
              <img
                src={buildTeamLogoUrl(team.logo) || ''}
                alt={team.name}
                className="w-12 h-12 object-contain rounded-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                }}
              />
            ) : (
              <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-gray-400" />
              </div>
            )}

            <div>
              <h1 className="text-2xl font-bold text-gray-900">{team.name}</h1>
              <div className="flex items-center space-x-2 text-gray-600">
                {team.country ? (
                  <>
                    <img
                      src={buildCountryFlagUrl(team.country) || ''}
                      alt={`${team.country} flag`}
                      className="w-4 h-3 object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                    <span>{team.country}</span>
                  </>
                ) : (
                  <span className="text-gray-400 italic">Country not available</span>
                )}
                {team.code && (
                  <>
                    <span>•</span>
                    <span className="font-mono text-sm">{team.code}</span>
                  </>
                )}
                {team.national && (
                  <>
                    <span>•</span>
                    <Badge variant="outline" className="text-xs">
                      National Team
                    </Badge>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button onClick={fetchTeam}>Refresh</Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="w-5 h-5" />
                <span>Team Information (Test Page)</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-gray-500 flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    Team Name
                  </label>
                  <p className="text-lg font-medium mt-1">{team.name}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500 flex items-center gap-2">
                    <Hash className="w-4 h-4" />
                    Team Code
                  </label>
                  {team.code ? (
                    <p className="text-lg font-medium font-mono mt-1">{team.code}</p>
                  ) : (
                    <p className="text-sm text-gray-400 italic mt-1">Not available</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500 flex items-center gap-2">
                    <Flag className="w-4 h-4" />
                    Country
                  </label>
                  {team.country ? (
                    <div className="flex items-center space-x-2 mt-1">
                      <img
                        src={buildCountryFlagUrl(team.country) || ''}
                        alt={`${team.country} flag`}
                        className="w-6 h-4 object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                      <span className="text-lg font-medium">{team.country}</span>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-400 italic mt-1">Not available</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500 flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Founded
                  </label>
                  {team.founded ? (
                    <p className="text-lg font-medium mt-1">{team.founded}</p>
                  ) : (
                    <p className="text-sm text-gray-400 italic mt-1">Not available</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Trophy className="w-5 h-5" />
                Team Logo
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center">
                {buildTeamLogoUrl(team.logo) ? (
                  <img
                    src={buildTeamLogoUrl(team.logo) || ''}
                    alt={team.name}
                    className="w-32 h-32 object-contain"
                  />
                ) : (
                  <div className="w-32 h-32 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Users className="w-16 h-16 text-gray-400" />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

'use client';

import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useTeam, useTeamLeaguesSeasons } from '@/lib/hooks/useTeams';
import { buildTeamLogoUrl, buildCountryFlagUrl, buildLeagueLogoUrl } from '@/lib/utils/image';
import {
      ArrowLeft,
      BarChart3,
      Trophy,
      Users,
      Calendar,
      Globe,
      Award,
      Activity,
      Crown,
      Zap
} from 'lucide-react';

export default function TeamStatisticsPage() {
      const params = useParams();
      const router = useRouter();
      const teamId = parseInt(params.id as string);

      // Fetch team details and leagues-seasons data
      const { team, isLoading: teamLoading, error: teamError } = useTeam(teamId);
      const { leaguesSeasons, isLoading: leaguesSeasonsLoading, error: leaguesSeasonsError } = useTeamLeaguesSeasons(teamId);

      const isLoading = teamLoading || leaguesSeasonsLoading;
      const error = teamError || leaguesSeasonsError;

      const handleViewTeamDetails = () => {
            router.push(`/dashboard/teams/${teamId}`);
      };

      if (isLoading) {
            return (
                  <div className="space-y-6">
                        <div className="flex items-center space-x-4">
                              <Skeleton className="h-10 w-20" />
                              <Skeleton className="h-8 w-64" />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                              {Array.from({ length: 8 }).map((_, i) => (
                                    <Skeleton key={i} className="h-24" />
                              ))}
                        </div>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                              <Skeleton className="h-96" />
                              <Skeleton className="h-96" />
                        </div>
                  </div>
            );
      }

      if (error || !team) {
            return (
                  <div className="space-y-6">
                        <div className="flex items-center space-x-4">
                              <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.back()}
                              >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>
                        </div>

                        <Card>
                              <CardContent className="flex items-center justify-center h-96">
                                    <div className="text-center">
                                          <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                Team statistics not available
                                          </h3>
                                          <p className="text-gray-500">
                                                Unable to load team leagues and seasons data at this time.
                                          </p>
                                    </div>
                              </CardContent>
                        </Card>
                  </div>
            );
      }

      // Get leagues and seasons data
      const data = leaguesSeasons;

      // Calculate statistics from leagues-seasons data
      const activeParticipations = data?.participations?.filter(p => p.isCurrentlyActive) || [];
      const totalSeasons = data?.participations?.reduce((sum, p) => sum + p.seasons.length, 0) || 0;

      // Group participations by league type
      const leagueParticipations = data?.participations?.filter(p => p.league.type === 'league') || [];
      const cupParticipations = data?.participations?.filter(p => p.league.type === 'cup') || [];

      return (
            <div className="space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                              <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.back()}
                              >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                              </Button>

                              <div className="flex items-center space-x-3">
                                    {buildTeamLogoUrl(team.logo) ? (
                                          <img
                                                src={buildTeamLogoUrl(team.logo) || ''}
                                                alt={team.name}
                                                className="w-10 h-10 object-contain rounded-full"
                                                onError={(e) => {
                                                      const target = e.target as HTMLImageElement;
                                                      target.style.display = 'none';
                                                }}
                                          />
                                    ) : (
                                          <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                                <Users className="w-5 h-5 text-gray-400" />
                                          </div>
                                    )}

                                    <div>
                                          <h1 className="text-2xl font-bold text-gray-900">{team.name} - Leagues & Seasons</h1>
                                          <div className="flex items-center space-x-2 text-gray-600">
                                                {team.country && (
                                                      <>
                                                            <img
                                                                  src={buildCountryFlagUrl(team.country) || ''}
                                                                  alt={`${team.country} flag`}
                                                                  className="w-4 h-3 object-cover"
                                                                  onError={(e) => {
                                                                        e.currentTarget.style.display = 'none';
                                                                  }}
                                                            />
                                                            <span>{team.country}</span>
                                                      </>
                                                )}
                                                {team.code && (
                                                      <>
                                                            <span>•</span>
                                                            <span className="font-mono text-sm">{team.code}</span>
                                                      </>
                                                )}
                                          </div>
                                    </div>
                              </div>
                        </div>

                        <Button
                              variant="outline"
                              size="sm"
                              onClick={handleViewTeamDetails}
                        >
                              <Eye className="w-4 h-4 mr-2" />
                              View Team
                        </Button>
                  </div>

                  {/* Overview Statistics */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {/* Total Leagues */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-gray-900">{data?.totalLeagues || 0}</p>
                                                <p className="text-sm text-gray-600">Total Leagues</p>
                                          </div>
                                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <Trophy className="w-6 h-6 text-blue-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Total Seasons */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-green-600">{totalSeasons}</p>
                                                <p className="text-sm text-gray-600">Total Seasons</p>
                                          </div>
                                          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                                <Calendar className="w-6 h-6 text-green-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Active Participations */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-emerald-600">{activeParticipations.length}</p>
                                                <p className="text-sm text-gray-600">Currently Active</p>
                                          </div>
                                          <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                                                <Activity className="w-6 h-6 text-emerald-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>

                        {/* Current Season */}
                        <Card>
                              <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                          <div>
                                                <p className="text-2xl font-bold text-purple-600">{data?.currentSeason || 'N/A'}</p>
                                                <p className="text-sm text-gray-600">Current Season</p>
                                          </div>
                                          <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                                <Crown className="w-6 h-6 text-purple-600" />
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* League Participations */}
                        <Card>
                              <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                          <Trophy className="w-5 h-5" />
                                          <span>League Participations ({leagueParticipations.length})</span>
                                    </CardTitle>
                              </CardHeader>
                              <CardContent className="space-y-4 max-h-96 overflow-y-auto">
                                    {leagueParticipations.length > 0 ? (
                                          leagueParticipations.map((participation, index) => (
                                                <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                                                      <div className="flex-shrink-0">
                                                            {buildLeagueLogoUrl(participation.league.logo) ? (
                                                                  <img
                                                                        src={buildLeagueLogoUrl(participation.league.logo) || ''}
                                                                        alt={participation.league.name}
                                                                        className="w-10 h-10 object-contain"
                                                                        onError={(e) => {
                                                                              const target = e.target as HTMLImageElement;
                                                                              target.style.display = 'none';
                                                                        }}
                                                                  />
                                                            ) : (
                                                                  <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                                                        <Trophy className="w-5 h-5 text-gray-400" />
                                                                  </div>
                                                            )}
                                                      </div>
                                                      <div className="flex-1 min-w-0">
                                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                                  {participation.league.name}
                                                            </p>
                                                            <div className="flex items-center space-x-2 mt-1">
                                                                  <Badge variant="outline" className="text-xs">
                                                                        <Globe className="w-3 h-3 mr-1" />
                                                                        {participation.league.country}
                                                                  </Badge>
                                                                  <Badge variant="secondary" className="text-xs">
                                                                        {participation.seasons.length} seasons
                                                                  </Badge>
                                                                  {participation.isCurrentlyActive && (
                                                                        <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                                                                              Active
                                                                        </Badge>
                                                                  )}
                                                            </div>
                                                            <p className="text-xs text-gray-500 mt-1">
                                                                  Seasons: {participation.seasons.join(', ')}
                                                            </p>
                                                      </div>
                                                </div>
                                          ))
                                    ) : (
                                          <div className="text-center py-8">
                                                <Trophy className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                                <p className="text-gray-500">No league participations found</p>
                                          </div>
                                    )}
                              </CardContent>
                        </Card>

                        {/* Cup Participations */}
                        <Card>
                              <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                          <Award className="w-5 h-5" />
                                          <span>Cup Participations ({cupParticipations.length})</span>
                                    </CardTitle>
                              </CardHeader>
                              <CardContent className="space-y-4 max-h-96 overflow-y-auto">
                                    {cupParticipations.length > 0 ? (
                                          cupParticipations.map((participation, index) => (
                                                <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                                                      <div className="flex-shrink-0">
                                                            {buildLeagueLogoUrl(participation.league.logo) ? (
                                                                  <img
                                                                        src={buildLeagueLogoUrl(participation.league.logo) || ''}
                                                                        alt={participation.league.name}
                                                                        className="w-10 h-10 object-contain"
                                                                        onError={(e) => {
                                                                              const target = e.target as HTMLImageElement;
                                                                              target.style.display = 'none';
                                                                        }}
                                                                  />
                                                            ) : (
                                                                  <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                                                        <Award className="w-5 h-5 text-gray-400" />
                                                                  </div>
                                                            )}
                                                      </div>
                                                      <div className="flex-1 min-w-0">
                                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                                  {participation.league.name}
                                                            </p>
                                                            <div className="flex items-center space-x-2 mt-1">
                                                                  <Badge variant="outline" className="text-xs">
                                                                        <Globe className="w-3 h-3 mr-1" />
                                                                        {participation.league.country}
                                                                  </Badge>
                                                                  <Badge variant="secondary" className="text-xs">
                                                                        {participation.seasons.length} seasons
                                                                  </Badge>
                                                                  {participation.isCurrentlyActive && (
                                                                        <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                                                                              Active
                                                                        </Badge>
                                                                  )}
                                                            </div>
                                                            <p className="text-xs text-gray-500 mt-1">
                                                                  Seasons: {participation.seasons.join(', ')}
                                                            </p>
                                                      </div>
                                                </div>
                                          ))
                                    ) : (
                                          <div className="text-center py-8">
                                                <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                                <p className="text-gray-500">No cup participations found</p>
                                          </div>
                                    )}
                              </CardContent>
                        </Card>

                        {/* Team Summary */}
                        <Card className="lg:col-span-2">
                              <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                          <Users className="w-5 h-5" />
                                          <span>Team Summary</span>
                                    </CardTitle>
                              </CardHeader>
                              <CardContent>
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                          <div className="text-center">
                                                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                                      <Trophy className="w-8 h-8 text-blue-600" />
                                                </div>
                                                <p className="text-2xl font-bold text-gray-900">{data?.totalLeagues || 0}</p>
                                                <p className="text-sm text-gray-600">Total Competitions</p>
                                          </div>
                                          <div className="text-center">
                                                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                                      <Calendar className="w-8 h-8 text-green-600" />
                                                </div>
                                                <p className="text-2xl font-bold text-gray-900">{totalSeasons}</p>
                                                <p className="text-sm text-gray-600">Total Seasons Played</p>
                                          </div>
                                          <div className="text-center">
                                                <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                                      <Zap className="w-8 h-8 text-emerald-600" />
                                                </div>
                                                <p className="text-2xl font-bold text-gray-900">{activeParticipations.length}</p>
                                                <p className="text-sm text-gray-600">Currently Active</p>
                                          </div>
                                    </div>
                              </CardContent>
                        </Card>
                  </div>
            </div>
      );
}

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { teamsApi } from '@/lib/api/teams';

export default function TestTeamApiPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testTeamApi = async (teamId: number) => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`🧪 Testing team API for ID: ${teamId}`);
      const team = await teamsApi.getTeamById(teamId);
      console.log('✅ Team API success:', team);
      setResult(team);
    } catch (err) {
      console.error('❌ Team API error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testDirectFetch = async (teamId: number) => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`🧪 Testing direct fetch for ID: ${teamId}`);
      
      // Get auth token
      const authStorage = localStorage.getItem('auth-storage');
      let token = null;
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        token = parsed.state?.accessToken;
      }
      
      if (!token) {
        token = localStorage.getItem('accessToken');
      }

      console.log('🔑 Using token:', token ? `${token.substring(0, 20)}...` : 'No token');

      const response = await fetch(`/api/teams/${teamId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        },
      });

      console.log('📡 Response status:', response.status);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Direct fetch success:', data);
      setResult(data.data);
    } catch (err) {
      console.error('❌ Direct fetch error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-bold">Team API Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Test Team API Service</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button 
              onClick={() => testTeamApi(127)} 
              disabled={loading}
              className="w-full"
            >
              Test Team ID 127 (via teamsApi)
            </Button>
            <Button 
              onClick={() => testTeamApi(2385)} 
              disabled={loading}
              className="w-full"
            >
              Test Team ID 2385 (via teamsApi)
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test Direct Fetch</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button 
              onClick={() => testDirectFetch(127)} 
              disabled={loading}
              className="w-full"
            >
              Test Team ID 127 (direct fetch)
            </Button>
            <Button 
              onClick={() => testDirectFetch(2385)} 
              disabled={loading}
              className="w-full"
            >
              Test Team ID 2385 (direct fetch)
            </Button>
          </CardContent>
        </Card>
      </div>

      {loading && (
        <Card>
          <CardContent className="p-4">
            <p>Loading...</p>
          </CardContent>
        </Card>
      )}

      {error && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      )}

      {result && (
        <Card className="border-green-200">
          <CardHeader>
            <CardTitle className="text-green-600">Success</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

'use client';

import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function SimpleTeamTestPage() {
  const params = useParams();
  const teamId = parseInt(params.id as string);
  const [team, setTeam] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTeam = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔄 Fetching team with ID:', teamId);
      
      // Get auth token
      const authStorage = localStorage.getItem('auth-storage');
      let token = null;
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        token = parsed.state?.accessToken;
      }
      
      if (!token) {
        token = localStorage.getItem('accessToken');
      }

      console.log('🔑 Using token:', token ? `${token.substring(0, 20)}...` : 'No token');

      const response = await fetch(`/api/teams/${teamId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        },
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Response error:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('✅ Team data received:', data);
      setTeam(data.data);
    } catch (err) {
      console.error('❌ Fetch error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (teamId) {
      fetchTeam();
    }
  }, [teamId]);

  if (loading) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-6">
            <p>Loading team {teamId}...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-600">Error Loading Team {teamId}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchTeam}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!team) {
    return (
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle>Team Not Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p>No team data found for ID: {teamId}</p>
            <Button onClick={fetchTeam} className="mt-4">Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <Card>
        <CardHeader>
          <CardTitle>Team Details - {team.name}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <strong>ID:</strong> {team.id}
            </div>
            <div>
              <strong>External ID:</strong> {team.externalId}
            </div>
            <div>
              <strong>Name:</strong> {team.name}
            </div>
            <div>
              <strong>Code:</strong> {team.code || 'Not available'}
            </div>
            <div>
              <strong>Country:</strong> {team.country || 'Not available'}
            </div>
            <div>
              <strong>Founded:</strong> {team.founded || 'Not available'}
            </div>
            <div>
              <strong>Logo:</strong> {team.logo}
            </div>
            <div>
              <strong>National:</strong> {team.national ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Venue:</strong> {team.venue || 'Not available'}
            </div>
            <div>
              <strong>Season:</strong> {team.season || 'Not available'}
            </div>
            <div>
              <strong>League ID:</strong> {team.leagueId || 'Not available'}
            </div>
          </div>
          
          <div className="mt-6">
            <Button onClick={fetchTeam}>Refresh Data</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

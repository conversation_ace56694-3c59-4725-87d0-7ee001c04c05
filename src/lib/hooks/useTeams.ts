import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { teamsApi, TeamFilters } from '@/lib/api/teams';

export const useTeams = (filters: TeamFilters = {}) => {
      const teamsQuery = useQuery({
            queryKey: ['teams', filters],
            queryFn: () => teamsApi.getTeams(filters),
            staleTime: 10 * 60 * 1000, // 10 minutes - teams don't change often
      });

      return {
            teams: teamsQuery.data?.data || [],
            teamsMeta: teamsQuery.data?.meta,
            isLoading: teamsQuery.isLoading,
            error: teamsQuery.error,
            refetch: teamsQuery.refetch,
      };
};

export const useTeam = (externalId: number) => {
      const teamQuery = useQuery({
            queryKey: ['team', 'detail', externalId],
            queryFn: async () => {
                  console.log('🔄 useTeam - Fetching team with ID:', externalId);
                  try {
                        const result = await teamsApi.getTeamById(externalId);
                        console.log('✅ useTeam - Success:', result);
                        return result;
                  } catch (error) {
                        console.error('❌ useTeam - Error:', error);
                        throw error;
                  }
            },
            enabled: !!externalId && externalId > 0,
            staleTime: 5 * 60 * 1000, // 5 minutes
            retry: (failureCount, error) => {
                  console.log('🔄 useTeam - Retry attempt:', failureCount, error);
                  return failureCount < 3;
            },
            retryDelay: 1000,
      });

      console.log('🔍 useTeam - Query state:', {
            externalId,
            data: teamQuery.data,
            isLoading: teamQuery.isLoading,
            error: teamQuery.error,
            status: teamQuery.status,
            fetchStatus: teamQuery.fetchStatus
      });

      return {
            team: teamQuery.data,
            isLoading: teamQuery.isLoading,
            error: teamQuery.error,
            refetch: teamQuery.refetch,
      };
};

export const useTeamsByLeague = (league: number, season?: number) => {
      const teamsByLeagueQuery = useQuery({
            queryKey: ['teams', 'by-league', league, season],
            queryFn: () => teamsApi.getTeamsByLeague(league, season),
            enabled: !!league,
            staleTime: 15 * 60 * 1000, // 15 minutes
      });

      return {
            teams: teamsByLeagueQuery.data?.data || [],
            teamsMeta: teamsByLeagueQuery.data?.meta,
            isLoading: teamsByLeagueQuery.isLoading,
            error: teamsByLeagueQuery.error,
            refetch: teamsByLeagueQuery.refetch,
      };
};

export const useTeamsByCountry = (country: string) => {
      const teamsByCountryQuery = useQuery({
            queryKey: ['teams', 'by-country', country],
            queryFn: () => teamsApi.getTeamsByCountry(country),
            enabled: !!country,
            staleTime: 15 * 60 * 1000, // 15 minutes
      });

      return {
            teams: teamsByCountryQuery.data?.data || [],
            teamsMeta: teamsByCountryQuery.data?.meta,
            isLoading: teamsByCountryQuery.isLoading,
            error: teamsByCountryQuery.error,
            refetch: teamsByCountryQuery.refetch,
      };
};

export const useTeamStatistics = (teamId: number) => {
      const statisticsQuery = useQuery({
            queryKey: ['teams', 'statistics', teamId],
            queryFn: () => {
                  // For now, return mock data since we need league and season info
                  // In real implementation, this would call teamsApi.getTeamStatistics with proper params
                  return Promise.resolve({
                        totalMatches: 28,
                        wins: 18,
                        draws: 6,
                        losses: 4,
                        goalsScored: 54,
                        goalsConceded: 23,
                        cleanSheets: 12,
                        winPercentage: 64.3,
                        avgGoalsPerMatch: 1.93,
                        avgGoalsConcededPerMatch: 0.82,
                        homeRecord: { wins: 11, draws: 3, losses: 0 },
                        awayRecord: { wins: 7, draws: 3, losses: 4 },
                        recentForm: ['W', 'W', 'D', 'W', 'L'],
                  });
            },
            enabled: !!teamId,
            staleTime: 5 * 60 * 1000, // 5 minutes
      });

      return {
            statistics: statisticsQuery.data,
            isLoading: statisticsQuery.isLoading,
            error: statisticsQuery.error,
            refetch: statisticsQuery.refetch,
      };
};

export const useTeamMutations = () => {
      const queryClient = useQueryClient();

      const deleteTeam = useMutation({
            mutationFn: (externalId: number) => teamsApi.deleteTeam(externalId),
            onSuccess: () => {
                  // Invalidate and refetch teams data
                  queryClient.invalidateQueries({ queryKey: ['teams'] });
            },
      });

      const syncTeam = useMutation({
            mutationFn: (externalId: number) => teamsApi.syncTeam(externalId),
            onSuccess: (data, externalId) => {
                  // Invalidate and refetch team data
                  queryClient.invalidateQueries({ queryKey: ['team', 'detail', externalId] });
                  queryClient.invalidateQueries({ queryKey: ['teams'] });
                  // Update the cache with new data
                  queryClient.setQueryData(['team', 'detail', externalId], data);
            },
      });

      return {
            deleteTeam: deleteTeam.mutate,
            isDeleteLoading: deleteTeam.isLoading,
            deleteError: deleteTeam.error,
            syncTeam: syncTeam.mutate,
            isSyncLoading: syncTeam.isLoading,
            syncError: syncTeam.error,
      };
};

export const useSearchTeams = (query: string, filters: TeamFilters = {}) => {
      const searchQuery = useQuery({
            queryKey: ['teams', 'search', query, filters],
            queryFn: () => teamsApi.searchTeams(query, filters),
            enabled: !!query && query.length >= 2, // Only search with 2+ characters
            staleTime: 2 * 60 * 1000, // 2 minutes for search results
      });

      return {
            searchResults: searchQuery.data?.data || [],
            searchMeta: searchQuery.data?.meta,
            isSearchLoading: searchQuery.isLoading,
            searchError: searchQuery.error,
            refetchSearch: searchQuery.refetch,
      };
};

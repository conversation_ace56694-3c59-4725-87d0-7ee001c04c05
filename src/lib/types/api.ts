// API Response Types
export interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
}

// Authentication Types
export interface SystemUser {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  role: 'admin' | 'editor' | 'moderator';
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: SystemUser;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

// Football Data Types
export interface LeagueSeasonDetail {
  end: string;
  year: number;
  start: string;
  current: boolean;
  coverage: {
    odds: boolean;
    players: boolean;
    fixtures: {
      events: boolean;
      lineups: boolean;
      statistics_players: boolean;
      statistics_fixtures: boolean;
    };
    injuries: boolean;
    standings: boolean;
    top_cards: boolean;
    predictions: boolean;
    top_assists: boolean;
    top_scorers: boolean;
  };
}

export interface League {
  id: number;
  externalId: number;
  name: string;
  country: string;
  logo: string;
  flag: string;
  countryFlag?: string;
  season: number;
  active: boolean;
  season_detail?: LeagueSeasonDetail;
  type: string;
  isHot?: boolean;
}

export interface TeamVenue {
  id: number;
  name: string;
  address: string;
  city: string;
  capacity: number;
  surface: string;
  image: string;
}

export interface Team {
  id: number;
  externalId: number;
  name: string;
  code?: string | null;
  country?: string | null;
  founded?: number | null;
  logo: string;
  season?: number | null;
  leagueId?: number | null;
  national?: boolean;
  venue?: string | TeamVenue | null;
}

export interface TeamLeagueParticipation {
  league: {
    id: number;
    externalId: number;
    name: string;
    country: string;
    logo: string;
    type: 'league' | 'cup';
  };
  seasons: number[];
  isCurrentlyActive: boolean;
}

export interface TeamLeaguesSeasons {
  team: {
    id: number;
    externalId: number;
    name: string;
    country: string;
    logo: string;
  };
  participations: TeamLeagueParticipation[];
  totalLeagues: number;
  totalSeasons: number;
  currentSeason: number;
  format: string;
}

export interface TeamLeaguesSeasonsResponse {
  data: TeamLeaguesSeasons;
  status: number;
}

export interface TeamStatistics {
  teamId: number;
  leagueId: number;
  season: number;
  statistics: {
    played: {
      home: number;
      away: number;
      total: number;
    };
    wins: {
      home: number;
      away: number;
      total: number;
    };
    draws: {
      home: number;
      away: number;
      total: number;
    };
    loses: {
      home: number;
      away: number;
      total: number;
    };
    goals: {
      for: {
        total: {
          home: number;
          away: number;
          total: number;
        };
        average: {
          home: string;
          away: string;
          total: string;
        };
        minute: {
          [key: string]: {
            total: number | null;
            percentage: string | null;
          };
        };
        under_over: {
          [key: string]: {
            over: number;
            under: number;
          };
        };
      };
      against: {
        total: {
          home: number;
          away: number;
          total: number;
        };
        average: {
          home: string;
          away: string;
          total: string;
        };
        minute: {
          [key: string]: {
            total: number | null;
            percentage: string | null;
          };
        };
        under_over: {
          [key: string]: {
            over: number;
            under: number;
          };
        };
      };
    };
    biggest: {
      streak: {
        wins: number;
        draws: number;
        loses: number;
      };
      wins: {
        home: string | null;
        away: string | null;
      };
      loses: {
        home: string | null;
        away: string | null;
      };
      goals: {
        for: {
          home: number;
          away: number;
        };
        against: {
          home: number;
          away: number;
        };
      };
    };
    clean_sheet: {
      home: number;
      away: number;
      total: number;
    };
    failed_to_score: {
      home: number;
      away: number;
      total: number;
    };
    penalty: {
      scored: {
        total: number;
        percentage: string;
      };
      missed: {
        total: number;
        percentage: string;
      };
      total: number;
    };
    cards: {
      yellow: {
        [key: string]: {
          total: number | null;
          percentage: string | null;
        };
      };
      red: {
        [key: string]: {
          total: number | null;
          percentage: string | null;
        };
      };
    };
    lineups: Array<{
      formation: string;
      played: number;
    }>;
  };
}

export interface TeamStatisticsResponse {
  data: TeamStatistics;
  status: number;
}

export interface News {
  id: number;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featuredImage?: string;
  tags?: string[];
  status: 'draft' | 'published' | 'archived';
  publishedAt?: string;
  metaTitle?: string;
  metaDescription?: string;
  relatedLeagueId?: number;
  relatedTeamId?: number;
  relatedPlayerId?: number;
  relatedFixtureId?: number;
  viewCount: number;
  shareCount: number;
  likeCount: number;
  isFeatured: boolean;
  priority: number;
  category?: NewsCategory;
  categoryId?: number;
  authorId: number;
  createdAt: string;
  updatedAt: string;

  // Computed properties for compatibility
  author?: string;
  summary?: string;
  imageUrl?: string;
  isPublished?: boolean;
  isHot?: boolean;
  publishDate?: string;
}

export interface FixtureTeamStatistics {
  fixtureId: number;
  teamName: string;
  statistics: {
    shotsOnGoal: number;
    shotsOffGoal: number;
    totalShots: number;
    corners: number;
    offsides: number;
    yellowCards: number;
    redCards: number;
    possession: string; // e.g., "57%"
    fouls?: number;
  };
}

export interface FixtureStatisticsResponse {
  data: FixtureTeamStatistics[];
  status: number;
}

export interface FixtureEvent {
  team: {
    id: number;
    name: string;
    logo: string;
  };
  time: {
    elapsed: number;
    extra: number | null;
  };
  type: string; // "Goal", "Card", "subst", etc.
  detail: string; // "Normal Goal", "Yellow Card", "Substitution 1", etc.
  player: {
    id: number;
    name: string;
  };
  assist: {
    id: number | null;
    name: string | null;
  };
}

export interface FixtureEventsData {
  fixtureId: number;
  events: FixtureEvent[];
}

export interface FixtureEventsResponse {
  data: FixtureEventsData;
  status: number;
}

export interface Fixture {
  id: number;
  externalId: number;
  leagueId: number;
  leagueName: string;
  isHot: boolean;
  season: number;
  round: string;
  homeTeamId: number;
  homeTeamName: string;
  homeTeamLogo: string;
  awayTeamId: number;
  awayTeamName: string;
  awayTeamLogo: string;
  slug: string;
  date: string;
  venue: {
    id: number;
    name: string;
    city: string;
  };
  referee: string;
  status: string;
  statusLong: string;
  statusExtra: number;
  elapsed: number;
  goalsHome: number | null;
  goalsAway: number | null;
  scoreHalftimeHome: number | null;
  scoreHalftimeAway: number | null;
  scoreFulltimeHome: number | null;
  scoreFulltimeAway: number | null;
  periods: {
    first: number;
    second: number;
  };
  timestamp: string;
  venueName?: string;
  venueCity?: string;
}

// Broadcast Link Types
export interface BroadcastLink {
  id: number;
  fixtureId: number;
  linkName: string;
  linkUrl: string;
  linkComment: string;
  addedBy: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBroadcastLinkRequest {
  fixtureId: number;
  linkName: string;
  linkUrl: string;
  linkComment?: string;
}

export interface UpdateBroadcastLinkRequest {
  linkName?: string;
  linkUrl?: string;
  linkComment?: string;
}

// User Management Types
export interface RegisteredUser {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  tier: 'free' | 'premium' | 'enterprise';
  isActive: boolean;
  isEmailVerified: boolean;
  apiCallsUsed: number;
  apiCallsLimit: number | null;
  apiCallsRemaining: number | null;
  hasActiveSubscription: boolean;
  subscriptionEndDate?: string;
  lastLoginAt?: string;
  createdAt: string;
}

// Image Upload Types
export interface UploadedImage {
  id: string;
  originalName: string;
  filename: string;
  size: number;
  mimeType: string;
  category: 'leagues' | 'teams' | 'flags' | 'venues' | 'general';
  url: string;
  path: string;
  uploadedAt: string;
  uploadedBy: number;
  description?: string;
}

export interface NewsCategory {
  id: number;
  slug: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  sortOrder: number;
  isActive: boolean;
  isPublic: boolean;
  metaTitle?: string;
  metaDescription?: string;
  articleCount?: number;
  publishedArticleCount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CategoryFilters {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
  isPublic?: boolean;
}

export interface CreateCategoryData {
  slug: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive?: boolean;
  isPublic?: boolean;
  metaTitle?: string;
  metaDescription?: string;
}

export interface UpdateCategoryData {
  name?: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive?: boolean;
  isPublic?: boolean;
  metaTitle?: string;
  metaDescription?: string;
}

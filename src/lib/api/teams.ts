import { apiClient } from './client';
import { Team, PaginatedResponse, TeamLeaguesSeasons, TeamStatistics } from '@/lib/types/api';

export interface TeamFilters {
  page?: number;
  limit?: number;
  league?: number;
  season?: number;
  country?: string;
  search?: string;
}

export interface TeamStatistics {
  teamId: number;
  leagueId: number;
  season: number;
  fixtures: {
    played: { home: number; away: number; total: number };
    wins: { home: number; away: number; total: number };
    draws: { home: number; away: number; total: number };
    loses: { home: number; away: number; total: number };
  };
  goals: {
    for: { total: { home: number; away: number; total: number } };
    against: { total: { home: number; away: number; total: number } };
  };
}

// Helper function to get auth headers
const getAuthHeaders = (): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (typeof window !== 'undefined') {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        const token = parsed.state?.accessToken;
        if (token) {
          headers.Authorization = `Bearer ${token}`;
          return headers;
        }
      }
    } catch (error) {
      console.warn('Failed to parse auth storage:', error);
    }

    // Fallback to direct localStorage access
    const fallbackToken = localStorage.getItem('accessToken');
    if (fallbackToken) {
      headers.Authorization = `Bearer ${fallbackToken}`;
      return headers;
    }
  }

  return headers;
};

export const teamsApi = {
  // Use Next.js API proxy (similar to leagues)
  getTeams: async (filters: TeamFilters = {}): Promise<PaginatedResponse<Team>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await fetch(`/api/teams?${params.toString()}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch teams');
    }

    return await response.json();
  },

  // Use Next.js API proxy for team details
  getTeamById: async (externalId: number): Promise<Team> => {
    const response = await fetch(`/api/teams/${externalId}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch team details');
    }

    const data = await response.json();
    return data.data; // Extract team data from API response
  },

  // Requires authentication
  getTeamStatistics: async (
    league: number,
    season: number,
    team: number
  ): Promise<{ data: TeamStatistics; status: number }> => {
    const params = new URLSearchParams({
      league: league.toString(),
      season: season.toString(),
      team: team.toString(),
    });

    const response = await apiClient.get<{ data: TeamStatistics; status: number }>(
      `/football/teams/statistics?${params.toString()}`
    );
    return response;
  },

  // Helper methods for common operations
  getTeamsByLeague: async (league: number, season?: number): Promise<PaginatedResponse<Team>> => {
    const filters: TeamFilters = { league };
    if (season) filters.season = season;
    return teamsApi.getTeams(filters);
  },

  getTeamsByCountry: async (country: string): Promise<PaginatedResponse<Team>> => {
    return teamsApi.getTeams({ country });
  },

  searchTeams: async (query: string, filters: TeamFilters = {}): Promise<PaginatedResponse<Team>> => {
    // Note: This would need to be implemented on the backend
    // For now, we'll get all teams and filter client-side (not ideal for production)
    const teams = await teamsApi.getTeams(filters);
    const filteredTeams = teams.data.filter(team =>
      team.name.toLowerCase().includes(query.toLowerCase()) ||
      team.code?.toLowerCase().includes(query.toLowerCase())
    );

    return {
      data: filteredTeams,
      meta: {
        ...teams.meta,
        totalItems: filteredTeams.length,
        totalPages: Math.ceil(filteredTeams.length / (filters.limit || 10)),
      },
    };
  },

  // Get team leagues and seasons participation
  getTeamLeaguesSeasons: async (externalId: number, format: string = 'by-league'): Promise<TeamLeaguesSeasons> => {
    const response = await fetch(`/api/teams/${externalId}/leagues-seasons?format=${format}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch team leagues and seasons');
    }

    const data = await response.json();
    return data.data; // Extract team leagues-seasons data from API response
  },

  // Get team statistics for specific league and season
  getTeamStatistics: async (teamId: number, leagueId: number, season: number): Promise<TeamStatistics> => {
    const response = await fetch(`/api/teams/statistics?team=${teamId}&league=${leagueId}&season=${season}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch team statistics');
    }

    const data = await response.json();
    return data.data; // Extract team statistics data from API response
  },

  // Sync team data from external source (requires authentication)
  syncTeam: async (externalId: number): Promise<Team> => {
    const response = await fetch(`/api/teams/${externalId}?newdb=true`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to sync team data');
    }

    const data = await response.json();
    return data.data; // Extract team data from API response
  },

  // Delete team (requires admin access)
  deleteTeam: async (externalId: number): Promise<void> => {
    await apiClient.delete(`/football/teams/${externalId}`);
  },
};

'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useTeamStatistics } from '@/lib/hooks/useTeams';
import { TeamLeagueParticipation, TeamStatistics } from '@/lib/types/api';
import { buildLeagueLogoUrl } from '@/lib/utils/image';
import {
  BarChart3,
  Trophy,
  Target,
  Shield,
  TrendingUp,
  TrendingDown,
  Users,
  Calendar,
  Globe,
  Activity,
  Award,
  Zap
} from 'lucide-react';

interface TeamStatisticsSelectorProps {
  teamId: number;
  participations: TeamLeagueParticipation[];
}

export default function TeamStatisticsSelector({ teamId, participations }: TeamStatisticsSelectorProps) {
  const [selectedLeague, setSelectedLeague] = useState<number | null>(null);
  const [selectedSeason, setSelectedSeason] = useState<number | null>(null);

  // Get available seasons for selected league
  const selectedParticipation = participations.find(p => p.league.externalId === selectedLeague);
  const availableSeasons = selectedParticipation?.seasons || [];

  // Fetch statistics when both league and season are selected
  const { statistics, isLoading, error } = useTeamStatistics(
    teamId,
    selectedLeague || 0,
    selectedSeason || 0
  );

  const handleLeagueChange = (leagueId: string) => {
    const id = parseInt(leagueId);
    setSelectedLeague(id);
    setSelectedSeason(null); // Reset season when league changes
  };

  const handleSeasonChange = (season: string) => {
    setSelectedSeason(parseInt(season));
  };

  const renderStatisticsCards = (stats: TeamStatistics) => {
    const { statistics: data } = stats;
    
    return (
      <div className="space-y-6">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-gray-900">{data.played.total}</p>
                  <p className="text-sm text-gray-600">Matches Played</p>
                </div>
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-green-600">{data.wins.total}</p>
                  <p className="text-sm text-gray-600">Wins</p>
                </div>
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Trophy className="w-5 h-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-yellow-600">{data.draws.total}</p>
                  <p className="text-sm text-gray-600">Draws</p>
                </div>
                <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-red-600">{data.loses.total}</p>
                  <p className="text-sm text-gray-600">Losses</p>
                </div>
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <TrendingDown className="w-5 h-5 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Statistics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Goals Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5" />
                <span>Goals Statistics</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Goals Scored</p>
                  <p className="text-2xl font-bold text-blue-600">{data.goals.for.total.total}</p>
                  <p className="text-xs text-gray-500">Avg: {data.goals.for.average.total}/game</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Goals Conceded</p>
                  <p className="text-2xl font-bold text-orange-600">{data.goals.against.total.total}</p>
                  <p className="text-xs text-gray-500">Avg: {data.goals.against.average.total}/game</p>
                </div>
              </div>
              
              <div className="pt-4 border-t">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Goal Difference</span>
                  <span className={`text-lg font-bold ${
                    data.goals.for.total.total - data.goals.against.total.total >= 0 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    {data.goals.for.total.total - data.goals.against.total.total >= 0 ? '+' : ''}
                    {data.goals.for.total.total - data.goals.against.total.total}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Home vs Away Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="w-5 h-5" />
                <span>Home vs Away</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">Home Record</span>
                  <span className="text-sm text-gray-600">
                    {data.wins.home}W - {data.draws.home}D - {data.loses.home}L
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-l-full"
                    style={{
                      width: `${data.played.home > 0 ? (data.wins.home / data.played.home) * 100 : 0}%`
                    }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">Away Record</span>
                  <span className="text-sm text-gray-600">
                    {data.wins.away}W - {data.draws.away}D - {data.loses.away}L
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-l-full"
                    style={{
                      width: `${data.played.away > 0 ? (data.wins.away / data.played.away) * 100 : 0}%`
                    }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="w-5 h-5" />
                <span>Defensive Stats</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Clean Sheets</span>
                <Badge variant="outline">{data.clean_sheet.total}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Failed to Score</span>
                <Badge variant="outline">{data.failed_to_score.total}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Win Percentage</span>
                <Badge variant="secondary">
                  {data.played.total > 0 ? ((data.wins.total / data.played.total) * 100).toFixed(1) : 0}%
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Formations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-5 h-5" />
                <span>Formations Used</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {data.lineups.slice(0, 5).map((lineup, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-sm font-medium">{lineup.formation}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">{lineup.played} games</span>
                      <Badge variant="outline" className="text-xs">
                        {data.played.total > 0 ? ((lineup.played / data.played.total) * 100).toFixed(0) : 0}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* League and Season Selectors */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5" />
            <span>Select League & Season</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* League Selector */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">League</label>
              <Select value={selectedLeague?.toString() || ''} onValueChange={handleLeagueChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a league" />
                </SelectTrigger>
                <SelectContent>
                  {participations.map((participation) => (
                    <SelectItem 
                      key={participation.league.externalId} 
                      value={participation.league.externalId.toString()}
                    >
                      <div className="flex items-center space-x-2">
                        {buildLeagueLogoUrl(participation.league.logo) && (
                          <img
                            src={buildLeagueLogoUrl(participation.league.logo) || ''}
                            alt={participation.league.name}
                            className="w-4 h-4 object-contain"
                          />
                        )}
                        <span>{participation.league.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {participation.league.country}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Season Selector */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Season</label>
              <Select 
                value={selectedSeason?.toString() || ''} 
                onValueChange={handleSeasonChange}
                disabled={!selectedLeague}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a season" />
                </SelectTrigger>
                <SelectContent>
                  {availableSeasons.map((season) => (
                    <SelectItem key={season} value={season.toString()}>
                      {season}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Display */}
      {isLoading && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2">Loading statistics...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {error && (
        <Card className="border-red-200">
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <Award className="w-12 h-12 mx-auto mb-4" />
              <p>Failed to load statistics</p>
              <p className="text-sm mt-2">{error instanceof Error ? error.message : 'Unknown error'}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {statistics && !isLoading && !error && renderStatisticsCards(statistics)}

      {!selectedLeague && !selectedSeason && !isLoading && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-gray-500">
              <BarChart3 className="w-12 h-12 mx-auto mb-4" />
              <p>Select a league and season to view statistics</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
